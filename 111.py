import minimalmodbus
import serial

PORT = '/dev/ttyUSB0'
BAUD = 115200
SLAVE_ADDRESS = 1

# 初始化设备
instrument = minimalmodbus.Instrument(PORT, SLAVE_ADDRESS)
instrument.serial.baudrate = BAUD
instrument.serial.timeout = 2

# 测试读取寄存器
try:
    value = instrument.read_register(0x0102)
    print(f"从寄存器 0x0102 读取值：{value}")
except minimalmodbus.NoResponseError:
    print("设备无响应。请检查端口、波特率和从站地址。")
except Exception as e:
    print(f"错误：{e}")

# 测试写入寄存器
try:
    instrument.write_register(0x0104, 80, functioncode=6)
    print("成功写入速度：80")
except minimalmodbus.NoResponseError:
    print("写入时无响应。请检查连接。")
except Exception as e:
    print(f"错误：{e}")

# 关闭串口
instrument.serial.close()