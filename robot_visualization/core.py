"""
核心可视化类
Core Visualization Classes

包含RobotVisualizer核心类的实现
"""

import os
import sys
import numpy as np
import time
import threading
from typing import Optional, List, Dict, Union
from pathlib import Path

# 添加pinocchio路径
sys.path.append("/opt/openrobots/lib/python3.8/site-packages")

import pinocchio
from pinocchio.robot_wrapper import RobotWrapper
import crocoddyl

from .utils import validate_urdf_path


class RobotVisualizer:
    """
    机器人实时可视化核心类
    
    功能：
    - 加载URDF模型
    - 实时更新关节位置
    - 获取机器人状态信息
    - 支持多种可视化模式
    """
    
    def __init__(self, urdf_path: str, package_dirs: Optional[List[str]] = None, 
                 use_free_flyer: bool = True, meshcat_port: int = 7000):
        """
        初始化机器人可视化器
        
        Args:
            urdf_path: URDF文件路径
            package_dirs: 包含mesh文件的目录列表
            use_free_flyer: 是否使用自由飞行器关节（用于浮动基座）
            meshcat_port: Meshcat可视化端口
        """
        self.urdf_path = validate_urdf_path(urdf_path)
        self.package_dirs = package_dirs or []
        self.use_free_flyer = use_free_flyer
        self.meshcat_port = meshcat_port
        
        # 机器人模型相关
        self.robot = None
        self.model = None
        self.data = None
        self.display = None
        
        # 状态变量
        self.current_q = None
        self.is_initialized = False
        self.visualization_thread = None
        self.stop_visualization = False
        
        # 初始化机器人模型
        self._load_robot_model()
        self._setup_visualization()
        
    def _load_robot_model(self):
        """加载机器人URDF模型"""
        try:
            # 构建包目录列表
            package_dirs = self.package_dirs.copy()
            if self.urdf_path.parent not in package_dirs:
                package_dirs.append(str(self.urdf_path.parent))
            
            # 加载机器人模型
            if self.use_free_flyer:
                self.robot = RobotWrapper.BuildFromURDF(
                    str(self.urdf_path), 
                    package_dirs, 
                    pinocchio.JointModelFreeFlyer()
                )
            else:
                self.robot = RobotWrapper.BuildFromURDF(
                    str(self.urdf_path), 
                    package_dirs
                )
            
            self.model = self.robot.model
            self.data = self.model.createData()
            
            # 初始化关节位置
            self.current_q = pinocchio.utils.zero(self.model.nq)
            if self.use_free_flyer:
                self.current_q[6] = 1.0  # 设置四元数w分量为1
            
            print(f"✅ 成功加载机器人模型: {self.urdf_path.name}")
            print(f"📊 关节数量: {self.model.nq}")
            print(f"🔗 自由度: {self.model.nv}")
            
        except Exception as e:
            raise RuntimeError(f"加载机器人模型失败: {str(e)}")
    
    def _setup_visualization(self):
        """设置可视化显示"""
        try:
            self.display = crocoddyl.MeshcatDisplay(self.robot)
            self.is_initialized = True
            
            # 显示初始姿态
            self.display.display([self.current_q])
            
            print(f"🌐 可视化已启动，请访问: http://127.0.0.1:{self.meshcat_port}/static/")
            
        except Exception as e:
            raise RuntimeError(f"设置可视化失败: {str(e)}")
    
    def update_joint_positions(self, joint_positions: Union[np.ndarray, List[float]], 
                             base_pose: Optional[np.ndarray] = None):
        """
        更新关节位置
        
        Args:
            joint_positions: 关节位置数组
            base_pose: 基座位姿 [x, y, z, qx, qy, qz, qw] (仅在use_free_flyer=True时有效)
        """
        if not self.is_initialized:
            raise RuntimeError("可视化器未初始化")
        
        joint_positions = np.array(joint_positions)
        
        # 更新关节位置
        if self.use_free_flyer:
            if base_pose is not None:
                base_pose = np.array(base_pose)
                if len(base_pose) == 7:  # [x, y, z, qx, qy, qz, qw]
                    self.current_q[:7] = base_pose
                elif len(base_pose) == 6:  # [x, y, z, rx, ry, rz]
                    self.current_q[:3] = base_pose[:3]
                    # 将欧拉角转换为四元数
                    quat = pinocchio.utils.rpyToMatrix(base_pose[3:6])
                    self.current_q[3:7] = pinocchio.Quaternion(quat).coeffs()
            
            # 设置关节角度
            start_idx = 7
            end_idx = min(7 + len(joint_positions), self.model.nq)
            self.current_q[start_idx:end_idx] = joint_positions[:end_idx-start_idx]
        else:
            # 无浮动基座情况
            end_idx = min(len(joint_positions), self.model.nq)
            self.current_q[:end_idx] = joint_positions[:end_idx]
        
        # 更新显示
        self.display.display([self.current_q])
    
    def get_joint_names(self) -> List[str]:
        """获取关节名称列表"""
        if not self.is_initialized:
            return []
        
        joint_names = []
        for i in range(self.model.njoints):
            joint_names.append(self.model.names[i])
        return joint_names
    
    def get_frame_names(self) -> List[str]:
        """获取所有frame名称列表"""
        if not self.is_initialized:
            return []
        
        frame_names = []
        for i in range(self.model.nframes):
            frame_names.append(self.model.frames[i].name)
        return frame_names
    
    def get_frame_pose(self, frame_name: str) -> Optional[np.ndarray]:
        """
        获取指定frame的位姿
        
        Args:
            frame_name: frame名称
            
        Returns:
            4x4变换矩阵，如果frame不存在则返回None
        """
        if not self.is_initialized:
            return None
        
        try:
            frame_id = self.model.getFrameId(frame_name)
            pinocchio.forwardKinematics(self.model, self.data, self.current_q)
            pinocchio.updateFramePlacements(self.model, self.data)
            return self.data.oMf[frame_id].homogeneous
        except:
            return None
    
    def get_center_of_mass(self) -> np.ndarray:
        """获取质心位置"""
        if not self.is_initialized:
            return np.zeros(3)
        
        return pinocchio.centerOfMass(self.model, self.data, self.current_q)
    
    def reset_to_zero_position(self):
        """重置到零位"""
        if self.is_initialized:
            self.current_q = pinocchio.utils.zero(self.model.nq)
            if self.use_free_flyer:
                self.current_q[6] = 1.0  # 设置四元数w分量为1
            self.display.display([self.current_q])
    
    def get_joint_count(self, exclude_free_flyer: bool = True) -> int:
        """
        获取关节数量
        
        Args:
            exclude_free_flyer: 是否排除自由飞行器关节
            
        Returns:
            关节数量
        """
        if not self.is_initialized:
            return 0
        
        if exclude_free_flyer and self.use_free_flyer:
            return self.model.nq - 7
        else:
            return self.model.nq
    
    def get_current_joint_positions(self) -> np.ndarray:
        """获取当前关节位置"""
        if not self.is_initialized:
            return np.array([])
        
        if self.use_free_flyer:
            return self.current_q[7:]
        else:
            return self.current_q.copy()
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'stop_visualization'):
            self.stop_visualization = True
