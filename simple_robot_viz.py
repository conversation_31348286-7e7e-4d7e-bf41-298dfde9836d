#!/usr/bin/env python3
"""
简化的机器人可视化API
提供最简单的接口用于快速可视化机器人
"""

import numpy as np
from typing import Union, List, Optional
from robot_visualizer import RobotVisualizer


class SimpleRobotViz:
    """
    简化的机器人可视化接口
    
    使用方法:
    1. viz = SimpleRobotViz("path/to/robot.urdf")
    2. viz.update_joints([0.1, 0.2, 0.3, ...])
    3. viz.show_url()
    """
    
    def __init__(self, urdf_path: str, package_dirs: Optional[List[str]] = None):
        """
        初始化简化可视化器
        
        Args:
            urdf_path: URDF文件路径
            package_dirs: 包含mesh文件的目录列表（可选）
        """
        self.visualizer = RobotVisualizer(
            urdf_path=urdf_path,
            package_dirs=package_dirs,
            use_free_flyer=True
        )
        
        print(f"✅ 机器人模型加载成功!")
        print(f"📊 关节数量: {self.visualizer.model.nq}")
        print(f"🔗 可用关节: {self.visualizer.model.nq - 7} (排除浮动基座)")
        self.show_url()
    
    def update_joints(self, joint_positions: Union[np.ndarray, List[float]], 
                     base_position: Optional[List[float]] = None,
                     base_orientation: Optional[List[float]] = None):
        """
        更新关节位置（最简单的接口）
        
        Args:
            joint_positions: 关节角度列表/数组
            base_position: 基座位置 [x, y, z] (可选)
            base_orientation: 基座方向 [rx, ry, rz] 欧拉角 (可选)
        """
        base_pose = None
        
        if base_position is not None or base_orientation is not None:
            base_pose = np.zeros(7)  # [x, y, z, qx, qy, qz, qw]
            
            if base_position is not None:
                base_pose[:3] = base_position
            
            if base_orientation is not None:
                # 简化：只设置z轴旋转
                base_pose[6] = np.cos(base_orientation[2]/2)  # qw
                base_pose[5] = np.sin(base_orientation[2]/2)  # qz
            else:
                base_pose[6] = 1.0  # qw = 1
        
        self.visualizer.update_joint_positions(joint_positions, base_pose)
    
    def reset(self):
        """重置到零位"""
        self.visualizer.reset_to_zero_position()
        print("🔄 已重置到零位")
    
    def show_url(self):
        """显示可视化URL"""
        print(f"🌐 打开浏览器访问: http://127.0.0.1:7000/static/")
    
    def get_joint_count(self) -> int:
        """获取关节数量（不包括浮动基座）"""
        return self.visualizer.model.nq - 7
    
    def get_joint_names(self) -> List[str]:
        """获取关节名称"""
        return self.visualizer.get_joint_names()[1:]  # 跳过root_joint
    
    def demo_walk(self, duration: float = 5.0):
        """演示步行动画"""
        from robot_visualizer import create_walking_animation
        import time
        
        print(f"🚶 开始步行演示，持续 {duration} 秒...")
        
        walking_func = create_walking_animation(self.visualizer)
        start_time = time.time()
        
        while time.time() - start_time < duration:
            joint_positions = walking_func()
            self.update_joints(joint_positions)
            time.sleep(1/30.0)  # 30 FPS
        
        self.reset()
        print("✅ 步行演示完成")
    
    def demo_joints(self, joint_indices: Optional[List[int]] = None, 
                   amplitude: float = 1.0, duration: float = 0.5):
        """
        演示关节运动
        
        Args:
            joint_indices: 要测试的关节索引列表，None表示测试所有关节
            amplitude: 运动幅度（弧度）
            duration: 每个关节运动持续时间（秒）
        """
        import time
        
        joint_count = self.get_joint_count()
        
        if joint_indices is None:
            joint_indices = list(range(min(joint_count, 20)))  # 默认测试前20个关节
        
        print(f"🔧 开始关节运动演示，测试 {len(joint_indices)} 个关节...")
        
        for i, joint_idx in enumerate(joint_indices):
            if joint_idx >= joint_count:
                continue
                
            joint_positions = np.zeros(joint_count)
            joint_positions[joint_idx] = amplitude
            
            self.update_joints(joint_positions)
            print(f"  关节 {joint_idx+1}: {amplitude:.2f} 弧度")
            time.sleep(duration)
        
        self.reset()
        print("✅ 关节演示完成")


# 便捷函数
def quick_viz(urdf_path: str, package_dirs: Optional[List[str]] = None) -> SimpleRobotViz:
    """
    快速创建可视化器的便捷函数
    
    Args:
        urdf_path: URDF文件路径
        package_dirs: 包含mesh文件的目录列表
        
    Returns:
        SimpleRobotViz实例
    """
    return SimpleRobotViz(urdf_path, package_dirs)


def demo_xbot():
    """XBot机器人演示"""
    urdf_path = '/home/<USER>/humanoid-gym/resources/robots/XBot/urdf/XBot-L.urdf'
    package_dirs = ['/home/<USER>/humanoid-gym/resources/robots/XBot/']
    
    try:
        print("🤖 XBot机器人可视化演示")
        print("=" * 40)
        
        # 创建可视化器
        viz = quick_viz(urdf_path, package_dirs)
        
        # 演示关节运动
        viz.demo_joints(joint_indices=[0, 1, 2, 6, 7, 8], duration=1.0)
        
        # 演示步行
        viz.demo_walk(duration=8.0)
        
        # 手动设置一些关节位置
        print("🎯 设置自定义姿态...")
        custom_joints = np.zeros(viz.get_joint_count())
        custom_joints[0] = 0.3   # 左髋俯仰
        custom_joints[3] = -0.6  # 左膝
        custom_joints[6] = 0.3   # 右髋俯仰
        custom_joints[9] = -0.6  # 右膝
        
        viz.update_joints(custom_joints)
        
        print("✅ 演示完成！可视化窗口保持打开状态")
        return viz
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        return None


if __name__ == "__main__":
    # 运行XBot演示
    viz = demo_xbot()
    
    if viz is not None:
        print("\n" + "="*50)
        print("💡 使用提示:")
        print("1. viz.update_joints([0.1, 0.2, ...])  # 更新关节位置")
        print("2. viz.reset()                         # 重置到零位")
        print("3. viz.demo_walk()                     # 步行演示")
        print("4. viz.demo_joints()                   # 关节演示")
        print("5. viz.show_url()                      # 显示可视化URL")
        print("="*50)
        
        # 保持程序运行
        try:
            input("\n按回车键退出...")
        except KeyboardInterrupt:
            print("\n👋 再见!")
