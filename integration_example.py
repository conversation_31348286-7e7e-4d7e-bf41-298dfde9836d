#!/usr/bin/env python3
"""
机器人可视化模块集成示例
展示如何在实际项目中使用可视化模块
"""

import numpy as np
import time
import threading
from typing import List, Optional
from simple_robot_viz import quick_viz


class RobotController:
    """
    模拟的机器人控制器类
    展示如何集成可视化功能
    """
    
    def __init__(self, urdf_path: str, package_dirs: Optional[List[str]] = None):
        """
        初始化机器人控制器
        
        Args:
            urdf_path: URDF文件路径
            package_dirs: 包含mesh文件的目录列表
        """
        # 创建可视化器
        self.visualizer = quick_viz(urdf_path, package_dirs)
        
        # 机器人状态
        self.joint_count = self.visualizer.get_joint_count()
        self.current_joints = np.zeros(self.joint_count)
        self.target_joints = np.zeros(self.joint_count)
        
        # 控制参数
        self.control_frequency = 50  # Hz
        self.is_running = False
        self.control_thread = None
        
        print(f"🤖 机器人控制器初始化完成")
        print(f"📊 关节数量: {self.joint_count}")
        print(f"🎛️  控制频率: {self.control_frequency} Hz")
    
    def set_joint_targets(self, target_positions: List[float]):
        """
        设置关节目标位置
        
        Args:
            target_positions: 目标关节位置列表
        """
        if len(target_positions) <= self.joint_count:
            self.target_joints[:len(target_positions)] = target_positions
            print(f"🎯 设置关节目标: {target_positions[:5]}...")
        else:
            print(f"⚠️  关节数量不匹配: 期望 <= {self.joint_count}, 得到 {len(target_positions)}")
    
    def start_control_loop(self):
        """启动控制循环"""
        if self.is_running:
            print("⚠️  控制循环已在运行")
            return
        
        self.is_running = True
        self.control_thread = threading.Thread(target=self._control_worker)
        self.control_thread.daemon = True
        self.control_thread.start()
        
        print("🚀 控制循环已启动")
    
    def stop_control_loop(self):
        """停止控制循环"""
        if not self.is_running:
            return
        
        self.is_running = False
        if self.control_thread:
            self.control_thread.join(timeout=1.0)
        
        print("🛑 控制循环已停止")
    
    def _control_worker(self):
        """控制循环工作线程"""
        dt = 1.0 / self.control_frequency
        
        while self.is_running:
            try:
                # 简单的PD控制器
                error = self.target_joints - self.current_joints
                self.current_joints += error * 0.1  # 简单的比例控制
                
                # 更新可视化
                self.visualizer.update_joints(self.current_joints)
                
                time.sleep(dt)
                
            except Exception as e:
                print(f"❌ 控制循环错误: {str(e)}")
                break
    
    def execute_trajectory(self, trajectory: List[List[float]], duration: float = 5.0):
        """
        执行轨迹
        
        Args:
            trajectory: 轨迹点列表，每个点是关节位置列表
            duration: 执行总时间（秒）
        """
        if not trajectory:
            return
        
        print(f"🎬 执行轨迹，{len(trajectory)} 个点，持续 {duration} 秒")
        
        start_time = time.time()
        
        while time.time() - start_time < duration:
            # 计算当前时间在轨迹中的位置
            progress = (time.time() - start_time) / duration
            index = min(int(progress * len(trajectory)), len(trajectory) - 1)
            
            # 设置目标位置
            self.set_joint_targets(trajectory[index])
            
            time.sleep(0.1)  # 轨迹更新频率
        
        print("✅ 轨迹执行完成")
    
    def demo_sine_wave(self, duration: float = 10.0):
        """正弦波演示"""
        print(f"🌊 正弦波演示，持续 {duration} 秒")
        
        start_time = time.time()
        
        while time.time() - start_time < duration:
            t = time.time() - start_time
            
            # 生成正弦波关节位置
            sine_joints = 0.3 * np.sin(2 * np.pi * 0.5 * t + np.arange(self.joint_count) * 0.5)
            self.set_joint_targets(sine_joints.tolist())
            
            time.sleep(0.1)
        
        # 回到零位
        self.set_joint_targets([0.0] * self.joint_count)
        time.sleep(2.0)
        
        print("✅ 正弦波演示完成")
    
    def get_current_state(self) -> dict:
        """获取当前机器人状态"""
        return {
            'current_joints': self.current_joints.tolist(),
            'target_joints': self.target_joints.tolist(),
            'is_running': self.is_running,
            'joint_count': self.joint_count
        }
    
    def shutdown(self):
        """关闭控制器"""
        self.stop_control_loop()
        self.visualizer.reset()
        print("🔌 机器人控制器已关闭")


def create_sample_trajectory() -> List[List[float]]:
    """创建示例轨迹"""
    trajectory = []
    
    # 创建一个简单的关节运动轨迹
    for i in range(50):
        t = i / 49.0  # 0 到 1
        
        # 简单的关节运动模式
        joints = [
            0.5 * np.sin(2 * np.pi * t),      # 关节0
            0.3 * np.cos(2 * np.pi * t),      # 关节1
            0.2 * np.sin(4 * np.pi * t),      # 关节2
            -0.4 * np.cos(2 * np.pi * t),     # 关节3
            0.1 * np.sin(6 * np.pi * t),      # 关节4
            0.0,                              # 关节5
        ]
        
        # 扩展到所有关节
        full_joints = joints + [0.0] * (12 - len(joints))
        trajectory.append(full_joints)
    
    return trajectory


def main():
    """主函数 - 完整的集成示例"""
    print("🚀 机器人可视化集成示例")
    print("=" * 50)
    
    # XBot机器人配置
    urdf_path = '/home/<USER>/humanoid-gym/resources/robots/XBot/urdf/XBot-L.urdf'
    package_dirs = ['/home/<USER>/humanoid-gym/resources/robots/XBot/']
    
    try:
        # 创建机器人控制器
        controller = RobotController(urdf_path, package_dirs)
        
        # 启动控制循环
        controller.start_control_loop()
        
        print("\n📋 演示序列:")
        
        # 1. 基本关节控制
        print("\n1️⃣ 基本关节控制演示")
        controller.set_joint_targets([0.5, 0.3, -0.2, 0.4, 0.0, 0.1])
        time.sleep(3)
        
        # 2. 轨迹执行
        print("\n2️⃣ 轨迹执行演示")
        trajectory = create_sample_trajectory()
        controller.execute_trajectory(trajectory, duration=5.0)
        
        # 3. 正弦波演示
        print("\n3️⃣ 正弦波演示")
        controller.demo_sine_wave(duration=5.0)
        
        # 4. 状态查询
        print("\n4️⃣ 状态查询演示")
        state = controller.get_current_state()
        print(f"当前状态: {state}")
        
        print("\n✅ 所有演示完成！")
        print("🌐 可视化窗口保持打开状态")
        
        # 保持程序运行
        print("\n按 Ctrl+C 退出...")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 收到退出信号")
        
        # 清理
        controller.shutdown()
        
    except Exception as e:
        print(f"❌ 集成示例失败: {str(e)}")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    print("\n👋 再见!")
    exit(0 if success else 1)
