# 文件：single_behavior_tree.py

import rclpy
from rclpy.action import ActionClient
from geometry_msgs.msg import PoseStamped
from nav2_msgs.action import NavigateToPose
import py_trees
import py_trees_ros

# ===== 节点定义 =====

class BatteryOK(py_trees.behaviour.Behaviour):
    def __init__(self, name="BatteryOK", threshold=30.0):
        super().__init__(name)
        self.threshold = threshold

    def update(self):
        battery_level = py_trees.blackboard.Blackboard().get("battery_level")
        if battery_level is None:
            battery_level = 50.0  # 模拟默认值
        return py_trees.common.Status.SUCCESS if battery_level > self.threshold else py_trees.common.Status.FAILURE

class DockAndCharge(py_trees.behaviour.Behaviour):
    def __init__(self):
        super().__init__(name="DockAndCharge")
    def update(self):
        print("[DockAndCharge] 模拟充电完毕")
        py_trees.blackboard.Blackboard().set("battery_level", 100.0)
        return py_trees.common.Status.SUCCESS

class NavigateTo(py_trees.behaviour.Behaviour):
    def __init__(self, name, goal_pose):
        super().__init__(name)
        self.goal_pose = goal_pose
        self.node = None
        self.client = None
        self._goal_future = None
        self._result_future = None

    def setup(self, **kwargs):
        self.node = kwargs.get("node")
        self.client = ActionClient(self.node, NavigateToPose, "/navigate_to_pose")
        return True

    def initialise(self):
        if not self.client.wait_for_server(timeout_sec=2.0):
            self.logger.error("导航服务器不可用")
            return
        goal_msg = NavigateToPose.Goal()
        goal_msg.pose = self.goal_pose
        self._goal_future = self.client.send_goal_async(goal_msg)

    def update(self):
        if self._goal_future is None or not self._goal_future.done():
            return py_trees.common.Status.RUNNING
        goal_handle = self._goal_future.result()
        if not goal_handle.accepted:
            return py_trees.common.Status.FAILURE
        if self._result_future is None:
            self._result_future = goal_handle.get_result_async()
        if not self._result_future.done():
            return py_trees.common.Status.RUNNING
        return py_trees.common.Status.SUCCESS

class AdjustElevator(py_trees.behaviour.Behaviour):
    def __init__(self, target_height):
        super().__init__(f"AdjustElevator({target_height})")
        self.target_height = target_height

    def update(self):
        print(f"[AdjustElevator] 升降至 {self.target_height} 米")
        return py_trees.common.Status.SUCCESS

class GraspObject(py_trees.behaviour.Behaviour):
    def __init__(self):
        super().__init__(name="GraspObject")
    def update(self):
        print("[GraspObject] 执行抓取动作")
        return py_trees.common.Status.SUCCESS

class PlaceObject(py_trees.behaviour.Behaviour):
    def __init__(self):
        super().__init__(name="PlaceObject")
    def update(self):
        print("[PlaceObject] 执行放置动作")
        return py_trees.common.Status.SUCCESS

class ReturnToIdlePose(py_trees.behaviour.Behaviour):
    def __init__(self):
        super().__init__(name="ReturnToIdlePose")
    def update(self):
        print("[ReturnToIdlePose] 机械臂归零")
        return py_trees.common.Status.SUCCESS

class AlignHeadToTarget(py_trees.behaviour.Behaviour):
    def __init__(self):
        super().__init__(name="AlignHeadToTarget")
    def update(self):
        print("[AlignHeadToTarget] 头部对准目标")
        return py_trees.common.Status.SUCCESS

# ===== 树构建 =====

def get_pose(x):
    pose = PoseStamped()
    pose.header.frame_id = "map"
    pose.pose.position.x = x
    pose.pose.orientation.w = 1.0
    return pose

def create_tree(node):
    root = py_trees.composites.Sequence("MainTask", memory=False)

    battery_check = py_trees.composites.Selector("BatteryCheck", memory=False)
    battery_check.add_children([
        BatteryOK(),
        py_trees.composites.Sequence("ChargeSequence", [
            NavigateTo("GoToCharger", get_pose(0.5)),
            DockAndCharge()
        ])
    ])

    tree = py_trees.composites.Sequence("TaskSequence", memory=False)
    tree.add_children([
        NavigateTo("GoToPick", get_pose(1.5)),
        AdjustElevator(0.8),
        AlignHeadToTarget(),
        GraspObject(),
        NavigateTo("GoToPlace", get_pose(2.5)),
        AdjustElevator(0.5),
        PlaceObject(),
        ReturnToIdlePose()
    ])

    root.add_children([battery_check, tree])
    return root

# ===== 主程序入口 =====

def main():
    rclpy.init()
    node = rclpy.create_node("behavior_tree_node")
    blackboard = py_trees.blackboard.Blackboard()
    blackboard.set("battery_level", 25.0)  # 初始低电量

    root = create_tree(node)
    behaviour_tree = py_trees_ros.trees.BehaviourTree(root, node)

    # 为树中依赖 ROS 2 节点的行为注入上下文
    for behaviour in root.iterate():
        if isinstance(behaviour, NavigateTo):
            behaviour.setup(node=node)

    rclpy.spin(node)
    rclpy.shutdown()

if __name__ == '__main__':
    main()
