# 机器人实时可视化模块

基于Pinocchio和Crocoddyl的机器人实时可视化工具，支持URDF模型加载和实时关节位置更新。

## 功能特性

- ✅ 支持任意URDF机器人模型
- ✅ 实时更新关节位置
- ✅ 浮动基座支持（自由飞行器）
- ✅ Web界面可视化（Meshcat）
- ✅ 简单易用的API接口
- ✅ 内置演示动画
- ✅ 多线程动画支持

## 文件结构

```
robot_visualizer.py      # 核心可视化类
simple_robot_viz.py      # 简化API接口
visualizer_example.py    # 详细使用示例
keshihua.py             # 原始脚本（已修复）
```

## 快速开始

### 1. 最简单的使用方式

```python
from simple_robot_viz import quick_viz

# 创建可视化器
viz = quick_viz("path/to/your/robot.urdf")

# 更新关节位置
joint_angles = [0.1, 0.2, 0.3, -0.5, 0.0, 0.1]  # 根据你的机器人关节数量
viz.update_joints(joint_angles)

# 重置到零位
viz.reset()
```

### 2. XBot机器人演示

```python
from simple_robot_viz import demo_xbot

# 运行完整演示
viz = demo_xbot()
```

### 3. 高级使用

```python
from robot_visualizer import RobotVisualizer
import numpy as np

# 创建可视化器
visualizer = RobotVisualizer(
    urdf_path="path/to/robot.urdf",
    package_dirs=["path/to/meshes/"],
    use_free_flyer=True
)

# 更新关节和基座位置
joint_positions = np.array([0.1, 0.2, 0.3, ...])
base_pose = [0, 0, 0.5, 0, 0, 0, 1]  # [x,y,z,qx,qy,qz,qw]
visualizer.update_joint_positions(joint_positions, base_pose)

# 获取机器人信息
joint_names = visualizer.get_joint_names()
frame_names = visualizer.get_frame_names()
com_position = visualizer.get_center_of_mass()
```

## API 参考

### SimpleRobotViz 类

#### 构造函数
```python
SimpleRobotViz(urdf_path, package_dirs=None)
```

#### 主要方法
- `update_joints(joint_positions, base_position=None, base_orientation=None)` - 更新关节位置
- `reset()` - 重置到零位
- `demo_walk(duration=5.0)` - 步行演示
- `demo_joints(joint_indices=None, amplitude=1.0, duration=0.5)` - 关节运动演示
- `get_joint_count()` - 获取关节数量
- `get_joint_names()` - 获取关节名称
- `show_url()` - 显示可视化URL

### RobotVisualizer 类

#### 构造函数
```python
RobotVisualizer(urdf_path, package_dirs=None, use_free_flyer=True, meshcat_port=7000)
```

#### 主要方法
- `update_joint_positions(joint_positions, base_pose=None)` - 更新关节位置
- `get_frame_pose(frame_name)` - 获取指定frame的位姿
- `get_center_of_mass()` - 获取质心位置
- `start_animation_loop(animation_function, fps=30.0)` - 启动动画循环
- `stop_animation_loop()` - 停止动画循环
- `reset_to_zero_position()` - 重置到零位

## 使用示例

### 实时控制接口

```python
import time
from simple_robot_viz import quick_viz

# 创建可视化器
viz = quick_viz("/path/to/robot.urdf")

# 模拟实时控制循环
for i in range(1000):
    # 从你的控制系统获取关节位置
    joint_positions = get_joint_positions_from_controller()
    
    # 更新可视化
    viz.update_joints(joint_positions)
    
    # 控制频率
    time.sleep(1/50.0)  # 50 Hz
```

### 动画循环

```python
from robot_visualizer import RobotVisualizer

visualizer = RobotVisualizer("robot.urdf")

def my_animation():
    # 返回当前时刻的关节位置
    t = time.time()
    return np.sin(t) * np.ones(12)  # 12个关节的正弦波

# 启动30fps动画
visualizer.start_animation_loop(my_animation, fps=30)

# 10秒后停止
time.sleep(10)
visualizer.stop_animation_loop()
```

## 依赖要求

```bash
# 系统依赖
sudo apt-get install python3-pinocchio

# Python依赖
pip install numpy
```

## 故障排除

### 1. 找不到URDF文件
确保URDF文件路径正确，并且包含mesh文件的目录在package_dirs中。

### 2. Frame不存在错误
使用`get_frame_names()`查看可用的frame名称。

### 3. 可视化不显示
检查浏览器是否能访问 http://127.0.0.1:7000/static/

### 4. 关节数量不匹配
使用`get_joint_count()`获取正确的关节数量。

## 从原始脚本迁移

原始的`keshihua.py`脚本已经修复并可正常运行。新的可视化模块提供了更好的封装：

```python
# 原始方式
# 复杂的初始化代码...
# display.display([q0])

# 新方式
viz = quick_viz("robot.urdf")
viz.update_joints(joint_positions)
```

## 许可证

本项目基于原始代码进行改进和封装，保持开源协议。
