import serial
import serial.tools.list_ports
import minimalmodbus
import threading

# 寄存器地址
POSITION_HIGH_8 = 0x0102
POSITION_LOW_8 = 0x0103
SPEED = 0x0104
FORCE = 0x0105
MOTION_TRIGGER = 0x0108

PORT = '/dev/ttyUSB0'
BAUD_RATES = [9600, 19200, 38400, 115200]
SLAVE_ADDRESSES = [1, 2, 3, 4, 5]

def test_connection(port, baudrate, slave_addr):
    try:
        instrument = minimalmodbus.Instrument(port, slave_addr)
        instrument.serial.baudrate = baudrate
        instrument.serial.timeout = 1
        instrument.serial.parity = serial.PARITY_NONE
        instrument.serial.stopbits = 1
        value = instrument.read_register(POSITION_HIGH_8)
        print(f"成功连接 - 端口: {port}, 波特率: {baudrate}, 从站地址: {slave_addr}, 读取 POSITION_HIGH_8: {value}")
        return instrument
    except Exception as e:
        print(f"连接失败 - 端口: {port}, 波特率: {baudrate}, 从站地址: {slave_addr}, 错误: {e}")
        return None

lock = threading.Lock()

def write_position_high8(instrument, value):
    with lock:
        try:
            instrument.write_register(POSITION_HIGH_8, value, functioncode=6)
            print(f"写入 POSITION_HIGH_8: {value}")
        except Exception as e:
            print(f"写入 POSITION_HIGH_8 错误: {e}")

def write_position_low8(instrument, value):
    with lock:
        try:
            instrument.write_register(POSITION_LOW_8, value, functioncode=6)
            print(f"写入 POSITION_LOW_8: {value}")
        except Exception as e:
            print(f"写入 POSITION_LOW_8 错误: {e}")

def write_position(instrument, value):
    with lock:
        try:
            instrument.write_long(POSITION_HIGH_8, value)
            print(f"写入位置: {value}")
        except Exception as e:
            print(f"写入位置错误: {e}")

def write_speed(instrument, speed):
    with lock:
        try:
            instrument.write_register(SPEED, speed, functioncode=6)
            print(f"写入速度: {speed}")
        except Exception as e:
            print(f"写入速度错误: {e}")

def write_force(instrument, force):
    with lock:
        try:
            instrument.write_register(FORCE, force, functioncode=6)
            print(f"写入力: {force}")
        except Exception as e:
            print(f"写入力错误: {e}")

def trigger_motion(instrument):
    with lock:
        try:
            instrument.write_register(MOTION_TRIGGER, 1, functioncode=6)
            print("触发运动")
        except Exception as e:
            print(f"触发运动错误: {e}")

if __name__ == '__main__':
    instrument = None
    for baud in BAUD_RATES:
        for addr in SLAVE_ADDRESSES:
            instrument = test_connection(PORT, baud, addr)
            if instrument:
                break
        if instrument:
            break

    if instrument:
        try:
            # 执行写入操作
            write_position(instrument, 500)
            write_speed(instrument, 80)
            write_force(instrument, 50)
            trigger_motion(instrument)
        except Exception as e:
            print(f"通信错误: {e}")
    else:
        print("未找到可用的 Modbus 设备")