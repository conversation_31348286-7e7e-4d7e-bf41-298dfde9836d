import socket
import json
import time

class ChassisNavigationClient:
    def __init__(self, server_ip, server_port):
        self.server_ip = server_ip
        self.server_port = server_port
        self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.client_socket.settimeout(10)  # Set timeout for socket operations

    def connect(self):
        try:
            self.client_socket.connect((self.server_ip, self.server_port))
            print(f"Connected to server at {self.server_ip}:{self.server_port}")
        except Exception as e:
            print(f"Failed to connect to server: {e}")
            self.client_socket.close()

    def send_command(self, command):
        try:
            self.client_socket.sendall(command.encode('utf-8'))
            response = self.client_socket.recv(4096).decode('utf-8')
            return json.loads(response)
        except Exception as e:
            print(f"Error sending command: {e}")
            return None

    def move_to_marker(self, marker_name):
        command = f"/api/move?marker={marker_name}"
        print(f"Sending command: {command}")
        return self.send_command(command)

    def move_to_location(self, x, y, theta):
        command = f"/api/move?location={x},{y},{theta}"
        print(f"Sending command: {command}")
        return self.send_command(command)
    
    def move_to_markers(self, markers, distance_tolerance=0.5, count=1, max_continuous_retries=5):
        if not markers or len(markers) < 2:
            print("Error: At least two markers are required for multi-target movement.")
            return None
        
        markers_param = ",".join(markers)
        command = f"/api/move?markers={markers_param}&distance_tolerance={distance_tolerance}&count={count}&max_continuous_retries={max_continuous_retries}"
        print(f"Sending command: {command}")
        return self.send_command(command)
    
    def get_robot_status(self):
        command = "/api/robot_status"
        return self.send_command(command)
    
    def wait_for_move_to_complete(self, timeout=None, check_interval=1):
        """
        Wait for the robot movement to complete, with optional timeout.

        :param timeout: Maximum time to wait in seconds (None means no timeout)
        :param check_interval: Time interval between status checks in seconds
        :return: True if move succeeded, False if failed or canceled
        """
        print("Waiting for robot movement to complete...")
        start_time = time.time()

        while True:
            # Check for timeout
            if timeout is not None and (time.time() - start_time) > timeout:
                print(f"Timeout of {timeout} seconds reached, cancelling move...")
                self.cancel_move()
                return False

            # Get robot status
            status = self.get_robot_status()
            if status and status.get("status") == "OK":
                move_status = status["results"].get("move_status")
                if move_status == "succeeded":
                    print("Move completed successfully.")
                    return True
                elif move_status == "failed":
                    print("Move failed.")
                    return False
                elif move_status == "running":
                    print("Move is still running, waiting...")
                else:
                    print(f"Unknown move status: {move_status}")
            else:
                print("Error fetching robot status, retrying...")

            time.sleep(check_interval)  # Check every few seconds

    def joy_control(self, linear_velocity, angular_velocity):
        """
        Directly control the robot's movement via linear and angular velocities.

        :param linear_velocity: Linear velocity (float, -0.5 to 0.5 m/s)
        :param angular_velocity: Angular velocity (float, -1.0 to 1.0 rad/s)
        :return: True if the command is successfully accepted, False otherwise
        """
        # Clamp the velocities to the safe ranges
        linear_velocity = max(min(linear_velocity, 0.5), -0.5)
        angular_velocity = max(min(angular_velocity, 1.0), -1.0)

        command = f"/api/joy_control?linear_velocity={linear_velocity}&angular_velocity={angular_velocity}"
        print(f"[INFO] Sending command: {command}")

        try:
            response = self.send_command(command)
            if response and response.get("status") == "OK":
                print("[INFO] Joy control command sent successfully.")
                return True
            else:
                error_message = response.get("error_message", "Unknown error") if response else "No response"
                print(f"[ERROR] Failed to send joy control command: {error_message}")
                return False
        except Exception as e:
            print(f"[EXCEPTION] Exception in joy_control: {e}")
            return False

    def get_robot_info(self):
        """
        Get basic robot information, such as product ID.

        :return: Dictionary with robot info if successful, None otherwise
        """
        command = "/api/robot_info"
        print(f"[INFO] Sending command: {command}")
        try:
            response = self.send_command(command)
            if response and response.get("status") == "OK":
                product_id = response.get("results", {}).get("product_id", "Unknown")
                print(f"[INFO] Robot Product ID: {product_id}")
                return response.get("results", {})
            else:
                error_message = response.get("error_message", "Unknown error") if response else "No response"
                print(f"[ERROR] Failed to get robot info: {error_message}")
                return None
        except Exception as e:
            print(f"[EXCEPTION] Exception in get_robot_info: {e}")
            return None
    
    def cancel_move(self):
        command = "/api/move/cancel"
        print(f"Sending cancel command: {command}")
        return self.send_command(command)
    
    def estop(self, flag):
        """
        Trigger emergency stop or release from emergency stop mode.

        :param flag: Boolean flag to enter (True) or exit (False) emergency stop mode
        :return: True if the command was accepted, False otherwise
        """
        command = f"/api/estop?flag={str(flag).lower()}"
        print(f"[INFO] Sending emergency stop command: {command}")
        
        try:
            response = self.send_command(command)
            if response and response.get("status") == "OK":
                status = "entered" if flag else "exited"
                print(f"[INFO] Emergency stop mode successfully {status}.")
                return True
            else:
                error_message = response.get("error_message", "Unknown error") if response else "No response"
                print(f"[ERROR] Failed to execute emergency stop: {error_message}")
                return False
        except Exception as e:
            print(f"[EXCEPTION] Exception in estop: {e}")
            return False

    def close(self):
        self.client_socket.close()
        print("Connection closed.")

if __name__ == "__main__":
    # Server configuration
    SERVER_IP = "*************"
    SERVER_PORT = 31001

    # Initialize the client
    client = ChassisNavigationClient(SERVER_IP, SERVER_PORT)

    # Connect to the server
    client.connect()

    # Example 1: Move to a target marker
    marker_name = "w_1"
    response = client.move_to_marker(marker_name)
    print("Response:", response)

    # Example 2: Move to a specific location
    # x, y, theta = 15.0, 4.0, 1.5707963
    # response = client.move_to_location(x, y, theta)
    # print("Response:", response)

    # Example 3: Move to multiple markers
    # markers = ["x_1", "x_2", "x_3", "x_4", "x_5", "x_6", "x_7", "x_8", "x_9", "x_10", "x_11", "x_12", "x_13", "x_14", "x_15", "x_16", "x_17", "x_18", "x_19", "x_20"]
    # response = client.move_to_markers(markers, distance_tolerance=0.5, count=2, max_continuous_retries=5)
    # print("Response:", response)

    # Example 4: cancel the move
    # cancel_response = client.cancel_move()
    # print("Cancel Response:", cancel_response)

    # Example 5: Get robot basic information
    # robot_info = client.get_robot_info()
    # if robot_info:
    #     print("Robot Info:", robot_info)
    # else:
    #     print("Failed to retrieve robot info.")

    # Example 6: joy control
    # linear_velocity = 0.2
    # angular_velocity = 0.5
    # response = client.joy_control(linear_velocity, angular_velocity)
    # print("Response:", response)

    # Example 7: Emergency stop
    # estop_response = client.estop(True)
    # print("Emergency stop response:", estop_response)

    # estop_response = client.estop(False)
    # print("Exit emergency stop response:", estop_response)



    # Wait for movement to complete before continuing
    if client.wait_for_move_to_complete():
        print("Proceeding with next steps...")
    else:
        print("Move failed, cannot proceed.")

    # Close the connection
    client.close()          