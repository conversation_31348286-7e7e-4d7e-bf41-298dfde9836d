#!/usr/bin/env python3
"""
机器人可视化模块测试脚本
验证所有功能是否正常工作
"""

import numpy as np
import time
import sys
from pathlib import Path


def test_simple_api():
    """测试简化API"""
    print("🧪 测试简化API...")
    
    try:
        from simple_robot_viz import SimpleRobotViz
        
        urdf_path = '/home/<USER>/humanoid-gym/resources/robots/XBot/urdf/XBot-L.urdf'
        package_dirs = ['/home/<USER>/humanoid-gym/resources/robots/XBot/']
        
        # 检查文件是否存在
        if not Path(urdf_path).exists():
            print(f"❌ URDF文件不存在: {urdf_path}")
            return False
        
        # 创建可视化器
        viz = SimpleRobotViz(urdf_path, package_dirs)
        
        # 测试基本功能
        joint_count = viz.get_joint_count()
        joint_names = viz.get_joint_names()
        
        print(f"✅ 关节数量: {joint_count}")
        print(f"✅ 关节名称: {joint_names[:5]}...")
        
        # 测试关节更新
        test_joints = np.zeros(joint_count)
        test_joints[0] = 0.5
        viz.update_joints(test_joints)
        print("✅ 关节位置更新成功")
        
        # 测试重置
        viz.reset()
        print("✅ 重置功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 简化API测试失败: {str(e)}")
        return False


def test_advanced_api():
    """测试高级API"""
    print("\n🧪 测试高级API...")
    
    try:
        from robot_visualizer import RobotVisualizer
        
        urdf_path = '/home/<USER>/humanoid-gym/resources/robots/XBot/urdf/XBot-L.urdf'
        package_dirs = ['/home/<USER>/humanoid-gym/resources/robots/XBot/']
        
        # 创建可视化器
        visualizer = RobotVisualizer(urdf_path, package_dirs, use_free_flyer=True)
        
        # 测试信息获取
        joint_names = visualizer.get_joint_names()
        frame_names = visualizer.get_frame_names()
        
        print(f"✅ 获取到 {len(joint_names)} 个关节")
        print(f"✅ 获取到 {len(frame_names)} 个frames")
        
        # 测试关节更新
        joint_positions = np.zeros(visualizer.model.nq - 7)
        joint_positions[0] = 0.3
        visualizer.update_joint_positions(joint_positions)
        print("✅ 高级关节更新成功")
        
        # 测试质心计算
        com = visualizer.get_center_of_mass()
        print(f"✅ 质心位置: {com}")
        
        # 测试frame位姿
        foot_frames = [name for name in frame_names if 'foot' in name.lower()]
        if foot_frames:
            pose = visualizer.get_frame_pose(foot_frames[0])
            if pose is not None:
                print(f"✅ Frame位姿获取成功: {foot_frames[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 高级API测试失败: {str(e)}")
        return False


def test_animation():
    """测试动画功能"""
    print("\n🧪 测试动画功能...")
    
    try:
        from simple_robot_viz import quick_viz
        
        urdf_path = '/home/<USER>/humanoid-gym/resources/robots/XBot/urdf/XBot-L.urdf'
        package_dirs = ['/home/<USER>/humanoid-gym/resources/robots/XBot/']
        
        viz = quick_viz(urdf_path, package_dirs)
        
        # 测试短时间动画
        print("🎬 测试2秒步行动画...")
        viz.demo_walk(duration=2.0)
        print("✅ 步行动画测试成功")
        
        # 测试关节演示
        print("🎬 测试关节演示...")
        viz.demo_joints(joint_indices=[0, 1, 2], duration=0.3)
        print("✅ 关节演示测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 动画功能测试失败: {str(e)}")
        return False


def test_real_time_simulation():
    """测试实时模拟"""
    print("\n🧪 测试实时模拟...")
    
    try:
        from simple_robot_viz import quick_viz
        
        urdf_path = '/home/<USER>/humanoid-gym/resources/robots/XBot/urdf/XBot-L.urdf'
        package_dirs = ['/home/<USER>/humanoid-gym/resources/robots/XBot/']
        
        viz = quick_viz(urdf_path, package_dirs)
        joint_count = viz.get_joint_count()
        
        print(f"🎬 模拟实时控制 (50Hz, 2秒)...")
        
        start_time = time.time()
        frame_count = 0
        
        while time.time() - start_time < 2.0:
            # 模拟实时数据
            t = time.time() - start_time
            joint_positions = 0.2 * np.sin(2 * np.pi * t * np.arange(joint_count))
            
            viz.update_joints(joint_positions)
            
            time.sleep(1/50.0)  # 50 Hz
            frame_count += 1
        
        fps = frame_count / 2.0
        print(f"✅ 实时模拟成功，平均帧率: {fps:.1f} FPS")
        
        viz.reset()
        return True
        
    except Exception as e:
        print(f"❌ 实时模拟测试失败: {str(e)}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理...")
    
    try:
        from simple_robot_viz import SimpleRobotViz
        
        # 测试不存在的文件
        try:
            viz = SimpleRobotViz("nonexistent.urdf")
            print("❌ 应该抛出文件不存在错误")
            return False
        except:
            print("✅ 正确处理文件不存在错误")
        
        # 测试正常情况下的错误恢复
        urdf_path = '/home/<USER>/humanoid-gym/resources/robots/XBot/urdf/XBot-L.urdf'
        package_dirs = ['/home/<USER>/humanoid-gym/resources/robots/XBot/']
        
        if Path(urdf_path).exists():
            viz = SimpleRobotViz(urdf_path, package_dirs)
            
            # 测试错误的关节数量
            try:
                wrong_joints = np.zeros(1000)  # 太多关节
                viz.update_joints(wrong_joints)
                print("✅ 处理错误关节数量")
            except:
                print("✅ 正确处理关节数量错误")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 机器人可视化模块测试套件")
    print("=" * 50)
    
    tests = [
        ("简化API", test_simple_api),
        ("高级API", test_advanced_api),
        ("动画功能", test_animation),
        ("实时模拟", test_real_time_simulation),
        ("错误处理", test_error_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！可视化模块工作正常")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False


if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n💡 可视化模块已准备就绪！")
        print("🌐 访问 http://127.0.0.1:7000/static/ 查看可视化")
    
    sys.exit(0 if success else 1)
