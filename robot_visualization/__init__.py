"""
机器人可视化模块
Robot Visualization Module

基于Pinocchio和Crocoddyl的机器人实时可视化工具包
支持URDF模型加载和实时关节位置更新

主要组件:
- RobotVisualizer: 核心可视化类
- SimpleRobotViz: 简化API接口
- AnimationController: 动画控制器
- utils: 工具函数

使用示例:
    from robot_visualization import quick_viz
    
    viz = quick_viz("path/to/robot.urdf")
    viz.update_joints([0.1, 0.2, 0.3])
    viz.demo_walk()

作者: AI Assistant
版本: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

# 导入主要类和函数
from .core import RobotVisualizer
from .simple_api import SimpleRobotViz, quick_viz
from .animation import AnimationController, create_walking_animation
from .utils import validate_urdf_path, get_default_joint_positions

# 导出的公共接口
__all__ = [
    # 核心类
    'RobotVisualizer',
    'SimpleRobotViz', 
    'AnimationController',
    
    # 便捷函数
    'quick_viz',
    'create_walking_animation',
    
    # 工具函数
    'validate_urdf_path',
    'get_default_joint_positions',
    
    # 版本信息
    '__version__',
    '__author__'
]

# 模块级别的配置
DEFAULT_MESHCAT_PORT = 7000
DEFAULT_CONTROL_FREQUENCY = 50  # Hz
DEFAULT_ANIMATION_FPS = 30

# 支持的机器人类型
SUPPORTED_ROBOTS = {
    'xbot': {
        'urdf_path': '/home/<USER>/humanoid-gym/resources/robots/XBot/urdf/XBot-L.urdf',
        'package_dirs': ['/home/<USER>/humanoid-gym/resources/robots/XBot/'],
        'joint_count': 12,
        'has_free_flyer': True
    }
}

def get_robot_config(robot_name: str) -> dict:
    """
    获取预定义机器人配置
    
    Args:
        robot_name: 机器人名称 (如 'xbot')
        
    Returns:
        机器人配置字典
    """
    return SUPPORTED_ROBOTS.get(robot_name.lower(), {})

def list_supported_robots():
    """列出所有支持的机器人类型"""
    print("支持的机器人类型:")
    for name, config in SUPPORTED_ROBOTS.items():
        print(f"  - {name}: {config.get('joint_count', 'N/A')} 关节")

# 模块初始化信息
print(f"🤖 Robot Visualization Module v{__version__} 已加载")
print(f"📦 可用组件: {', '.join(__all__[:4])}")
