import minimalmodbus
import serial
import threading
import time
from typing import Dict, List, Tuple, Union

class ChangingTekGripper:
    """知行工业平动手控制类，支持Modbus RTU通讯协议"""
    
    # 寄存器地址常量
    # 控制寄存器
    POSITION_HIGH = 0x0102  # 位置寄存器高16位
    POSITION_LOW = 0x0103   # 位置寄存器低16位
    SPEED = 0x0104          # 速度百分比
    FORCE = 0x0105          # 力百分比
    ACCELERATION = 0x0106   # 加速度百分比
    DECELERATION = 0x0107   # 减速度百分比
    MOTION_TRIGGER = 0x0108 # 运动触发位
    
    # 状态寄存器
    CURRENT_POSITION_HIGH = 0x0609  # 当前位置高16位
    CURRENT_POSITION_LOW = 0x060A   # 当前位置低16位
    CURRENT_SPEED = 0x060B          # 当前速度
    CURRENT_FORCE = 0x060C          # 当前力
    FORCE_REACHED = 0x0601          # 力到达标志
    POSITION_REACHED = 0x0602       # 位置到达标志
    SPEED_REACHED = 0x0603          # 速度到达标志
    READY_STATUS = 0x0604           # 就绪状态
    ALARM_CODE = 0x0612             # 报警代码
    
    # 系统寄存器
    HOMING = 0x0402                 # 回零控制
    SAVE_PARAMS = 0x0401            # 保存参数
    RESET_ALARM = 0x0403            # 复位报警
    
    def __init__(self, port: str, slave_address: int = 1, baudrate: int = 115200):
        """
        初始化夹爪控制器
        
        Args:
            port: 串口号，如'COM3'或'/dev/ttyUSB0'
            slave_address: 从站地址，默认1
            baudrate: 波特率，默认115200
        """
        self.port = port
        self.slave_address = slave_address
        self.baudrate = baudrate
        self.instrument = minimalmodbus.Instrument(port, slave_address)
        self.instrument.serial.baudrate = baudrate
        self.instrument.serial.timeout = 1
        self.connected = False
        self.lock = threading.Lock()  # 用于线程安全的锁
    
    def connect(self) -> bool:
        """连接到夹爪设备"""
        try:
            # 尝试读取设备信息来验证连接
            self.get_product_info()
            self.connected = True
            print(f"成功连接到夹爪设备，端口: {self.port}, 从站地址: {self.slave_address}")
            return True
        except Exception as e:
            print(f"连接失败: {str(e)}")
            self.connected = False
            return False
    
    def disconnect(self) -> None:
        """断开与夹爪的连接"""
        self.connected = False
        print("已断开与夹爪的连接")
    
    def _check_connection(self) -> None:
        """检查连接状态"""
        if not self.connected:
            raise Exception("未连接到夹爪设备，请先调用connect()方法")
    
    # ===== 基础控制接口 =====
    
    def set_position(self, position: int) -> bool:
        """
        设置夹爪目标位置
        
        Args:
            position: 目标位置值
        
        Returns:
            设置是否成功
        """
        self._check_connection()
        try:
            with self.lock:
                self.instrument.write_long(self.POSITION_HIGH, position)
                return True
        except Exception as e:
            print(f"设置位置失败: {str(e)}")
            return False
    
    def set_speed(self, speed_percent: int) -> bool:
        """
        设置夹爪运动速度
        
        Args:
            speed_percent: 速度百分比(0-100)
        
        Returns:
            设置是否成功
        """
        self._check_connection()
        if not (0 <= speed_percent <= 100):
            raise ValueError("速度百分比必须在0-100之间")
        
        try:
            with self.lock:
                self.instrument.write_register(self.SPEED, speed_percent, functioncode=6)
                return True
        except Exception as e:
            print(f"设置速度失败: {str(e)}")
            return False
    
    def set_force(self, force_percent: int) -> bool:
        """
        设置夹爪抓取力
        
        Args:
            force_percent: 力百分比(0-100)
        
        Returns:
            设置是否成功
        """
        self._check_connection()
        if not (0 <= force_percent <= 100):
            raise ValueError("力百分比必须在0-100之间")
        
        try:
            with self.lock:
                self.instrument.write_register(self.FORCE, force_percent, functioncode=6)
                return True
        except Exception as e:
            print(f"设置力失败: {str(e)}")
            return False
    
    def set_acceleration(self, accel_percent: int) -> bool:
        """
        设置夹爪加速度
        
        Args:
            accel_percent: 加速度百分比(0-100)
        
        Returns:
            设置是否成功
        """
        self._check_connection()
        if not (0 <= accel_percent <= 100):
            raise ValueError("加速度百分比必须在0-100之间")
        
        try:
            with self.lock:
                self.instrument.write_register(self.ACCELERATION, accel_percent, functioncode=6)
                return True
        except Exception as e:
            print(f"设置加速度失败: {str(e)}")
            return False
    
    def set_deceleration(self, decel_percent: int) -> bool:
        """
        设置夹爪减速度
        
        Args:
            decel_percent: 减速度百分比(1-100)
        
        Returns:
            设置是否成功
        """
        self._check_connection()
        if not (1 <= decel_percent <= 100):
            raise ValueError("减速度百分比必须在1-100之间")
        
        try:
            with self.lock:
                self.instrument.write_register(self.DECELERATION, decel_percent, functioncode=6)
                return True
        except Exception as e:
            print(f"设置减速度失败: {str(e)}")
            return False
    
    def trigger_motion(self) -> bool:
        """
        触发夹爪执行运动
        
        Returns:
            触发是否成功
        """
        self._check_connection()
        try:
            with self.lock:
                self.instrument.write_register(self.MOTION_TRIGGER, 1, functioncode=6)
                return True
        except Exception as e:
            print(f"触发运动失败: {str(e)}")
            return False
    
    def execute_movement(self, position: int, speed_percent: int = 80, 
                        force_percent: int = 50) -> bool:
        """
        执行完整的运动序列：设置位置、速度、力，然后触发运动
        
        Args:
            position: 目标位置
            speed_percent: 速度百分比(0-100)
            force_percent: 力百分比(0-100)
        
        Returns:
            运动是否成功触发
        """
        self._check_connection()
        try:
            success = self.set_position(position)
            success &= self.set_speed(speed_percent)
            success &= self.set_force(force_percent)
            success &= self.trigger_motion()
            return success
        except Exception as e:
            print(f"执行运动失败: {str(e)}")
            return False
    
    # ===== 状态读取接口 =====
    
    def get_current_position(self) -> int:
        """
        获取夹爪当前位置
        
        Returns:
            当前位置值
        """
        self._check_connection()
        try:
            with self.lock:
                return self.instrument.read_long(self.CURRENT_POSITION_HIGH)
        except Exception as e:
            print(f"读取当前位置失败: {str(e)}")
            return 0
    
    def get_current_speed(self) -> int:
        """
        获取夹爪当前速度
        
        Returns:
            当前速度百分比
        """
        self._check_connection()
        try:
            with self.lock:
                return self.instrument.read_register(self.CURRENT_SPEED)
        except Exception as e:
            print(f"读取当前速度失败: {str(e)}")
            return 0
    
    def get_current_force(self) -> int:
        """
        获取夹爪当前力
        
        Returns:
            当前力百分比
        """
        self._check_connection()
        try:
            with self.lock:
                return self.instrument.read_register(self.CURRENT_FORCE)
        except Exception as e:
            print(f"读取当前力失败: {str(e)}")
            return 0
    
    def get_status(self) -> Dict[str, bool]:
        """
        获取夹爪状态信息
        
        Returns:
            包含各种状态标志的字典
        """
        self._check_connection()
        try:
            with self.lock:
                force_reached = self.instrument.read_register(self.FORCE_REACHED) == 1
                position_reached = self.instrument.read_register(self.POSITION_REACHED) == 1
                speed_reached = self.instrument.read_register(self.SPEED_REACHED) == 1
                ready = self.instrument.read_register(self.READY_STATUS) == 1
                
                return {
                    'force_reached': force_reached,
                    'position_reached': position_reached,
                    'speed_reached': speed_reached,
                    'ready': ready
                }
        except Exception as e:
            print(f"读取状态失败: {str(e)}")
            return {
                'force_reached': False,
                'position_reached': False,
                'speed_reached': False,
                'ready': False
            }
    
    def get_alarm_status(self) -> Dict[str, bool]:
        """
        获取夹爪报警状态
        
        Returns:
            包含各种报警标志的字典
        """
        self._check_connection()
        try:
            with self.lock:
                alarm_code = self.instrument.read_register(self.ALARM_CODE)
                
                return {
                    'overheat': (alarm_code & 0x01) != 0,       # 过温警报
                    'stall': (alarm_code & 0x02) != 0,         # 堵转警报
                    'overspeed': (alarm_code & 0x04) != 0,     # 超速警报
                    'initialization_fault': (alarm_code & 0x08) != 0,  # 初始化故障
                    'overlimit': (alarm_code & 0x10) != 0,     # 超限检测
                    'drop_alarm': (alarm_code & 0x20) != 0      # 夹取掉落
                }
        except Exception as e:
            print(f"读取报警状态失败: {str(e)}")
            return {
                'overheat': False, 'stall': False, 'overspeed': False,
                'initialization_fault': False, 'overlimit': False, 'drop_alarm': False
            }
    
    def wait_for_position_reached(self, timeout: float = 10.0, 
                                 check_interval: float = 0.1) -> bool:
        """
        等待夹爪到达目标位置
        
        Args:
            timeout: 超时时间(秒)
            check_interval: 检查间隔(秒)
        
        Returns:
            是否成功到达目标位置
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            status = self.get_status()
            if status['position_reached']:
                return True
            time.sleep(check_interval)
        
        print(f"等待位置到达超时({timeout}秒)")
        return False
    
    # ===== 系统控制接口 =====
    
    def home(self) -> bool:
        """
        使夹爪回零
        
        Returns:
            回零命令是否成功发送
        """
        self._check_connection()
        try:
            with self.lock:
                self.instrument.write_register(self.HOMING, 1, functioncode=6)
                return True
        except Exception as e:
            print(f"回零失败: {str(e)}")
            return False
    
    def save_parameters(self) -> bool:
        """
        保存当前参数到非易失性存储器
        
        Returns:
            保存是否成功
        """
        self._check_connection()
        try:
            with self.lock:
                self.instrument.write_register(self.SAVE_PARAMS, 1, functioncode=6)
                return True
        except Exception as e:
            print(f"保存参数失败: {str(e)}")
            return False
    
    def reset_alarm(self) -> bool:
        """
        复位报警状态
        
        Returns:
            复位是否成功
        """
        self._check_connection()
        try:
            with self.lock:
                self.instrument.write_register(self.RESET_ALARM, 1, functioncode=6)
                return True
        except Exception as e:
            print(f"复位报警失败: {str(e)}")
            return False
    
    def get_product_info(self) -> Dict[str, str]:
        """
        获取产品信息
        
        Returns:
            包含产品信息的字典
        """
        self._check_connection()
        try:
            with self.lock:
                # 读取软件版本(假设在0x0801-0x0803寄存器)
                software_version = "未知"
                try:
                    sw_bytes = self.instrument.read_registers(0x0801, 3)
                    software_version = ''.join([chr(b) for b in sw_bytes if b != 0])
                except:
                    pass
                
                # 读取产品ID(假设在0x0804-0x080D寄存器)
                product_id = "未知"
                try:
                    id_bytes = self.instrument.read_registers(0x0804, 10)
                    product_id = ''.join([chr(b) for b in id_bytes if b != 0])
                except:
                    pass
                
                return {
                    'software_version': software_version,
                    'product_id': product_id,
                    'port': self.port,
                    'slave_address': self.slave_address
                }
        except Exception as e:
            print(f"获取产品信息失败: {str(e)}")
            return {
                'software_version': '未知',
                'product_id': '未知',
                'port': self.port,
                'slave_address': self.slave_address
            }

# 示例使用
if __name__ == "__main__":
    # 创建夹爪控制器实例
    gripper = ChangingTekGripper(port='/dev/ttyUSB0', slave_address=1)
    
    # 连接到夹爪
    if gripper.connect():
        # 打印产品信息
        print("产品信息:", gripper.get_product_info())
        
        # 回零操作
        print("执行回零操作...")
        gripper.home()
        time.sleep(2)  # 等待回零完成
        
        # 执行一个简单的开合动作
        print("执行开合动作...")
        gripper.execute_movement(position=10000, speed_percent=70, force_percent=50)
        gripper.wait_for_position_reached(timeout=5)
        
        time.sleep(1)
        
        gripper.execute_movement(position=5000, speed_percent=70, force_percent=50)
        gripper.wait_for_position_reached(timeout=5)
        
        # 读取当前状态
        status = gripper.get_status()
        position = gripper.get_current_position()
        force = gripper.get_current_force()
        
        print(f"当前位置: {position}, 当前力: {force}%")
        print(f"状态: {status}")
        
        # 断开连接
        gripper.disconnect()