#!/usr/bin/env python3
"""
机器人实时可视化模块
基于Pinocchio和Crocoddyl的MeshcatDisplay实现
支持URDF模型加载和实时关节位置更新
"""

import os
import sys
import numpy as np
import time
import threading
from typing import Optional, List, Dict, Union
from pathlib import Path

# 添加pinocchio路径
sys.path.append("/opt/openrobots/lib/python3.8/site-packages")

import pinocchio
from pinocchio.robot_wrapper import RobotWrapper
import crocoddyl


class RobotVisualizer:
    """
    机器人实时可视化类
    
    功能：
    - 加载URDF模型
    - 实时更新关节位置
    - 获取机器人状态信息
    - 支持多种可视化模式
    """
    
    def __init__(self, urdf_path: str, package_dirs: Optional[List[str]] = None, 
                 use_free_flyer: bool = True, meshcat_port: int = 7000):
        """
        初始化机器人可视化器
        
        Args:
            urdf_path: URDF文件路径
            package_dirs: 包含mesh文件的目录列表
            use_free_flyer: 是否使用自由飞行器关节（用于浮动基座）
            meshcat_port: Meshcat可视化端口
        """
        self.urdf_path = Path(urdf_path)
        self.package_dirs = package_dirs or []
        self.use_free_flyer = use_free_flyer
        self.meshcat_port = meshcat_port
        
        # 机器人模型相关
        self.robot = None
        self.model = None
        self.data = None
        self.display = None
        
        # 状态变量
        self.current_q = None
        self.is_initialized = False
        self.visualization_thread = None
        self.stop_visualization = False
        
        # 初始化机器人模型
        self._load_robot_model()
        self._setup_visualization()
        
    def _load_robot_model(self):
        """加载机器人URDF模型"""
        try:
            if not self.urdf_path.exists():
                raise FileNotFoundError(f"URDF文件不存在: {self.urdf_path}")
            
            # 构建包目录列表
            package_dirs = self.package_dirs.copy()
            if self.urdf_path.parent not in package_dirs:
                package_dirs.append(str(self.urdf_path.parent))
            
            # 加载机器人模型
            if self.use_free_flyer:
                self.robot = RobotWrapper.BuildFromURDF(
                    str(self.urdf_path), 
                    package_dirs, 
                    pinocchio.JointModelFreeFlyer()
                )
            else:
                self.robot = RobotWrapper.BuildFromURDF(
                    str(self.urdf_path), 
                    package_dirs
                )
            
            self.model = self.robot.model
            self.data = self.model.createData()
            
            # 初始化关节位置
            self.current_q = pinocchio.utils.zero(self.model.nq)
            if self.use_free_flyer:
                self.current_q[6] = 1.0  # 设置四元数w分量为1
            
            print(f"成功加载机器人模型: {self.urdf_path.name}")
            print(f"关节数量: {self.model.nq}")
            print(f"自由度: {self.model.nv}")
            
        except Exception as e:
            raise RuntimeError(f"加载机器人模型失败: {str(e)}")
    
    def _setup_visualization(self):
        """设置可视化显示"""
        try:
            self.display = crocoddyl.MeshcatDisplay(self.robot)
            self.is_initialized = True
            
            # 显示初始姿态
            self.display.display([self.current_q])
            
            print(f"可视化已启动，请访问: http://127.0.0.1:{self.meshcat_port}/static/")
            
        except Exception as e:
            raise RuntimeError(f"设置可视化失败: {str(e)}")
    
    def update_joint_positions(self, joint_positions: Union[np.ndarray, List[float]], 
                             base_pose: Optional[np.ndarray] = None):
        """
        更新关节位置
        
        Args:
            joint_positions: 关节位置数组
            base_pose: 基座位姿 [x, y, z, qx, qy, qz, qw] (仅在use_free_flyer=True时有效)
        """
        if not self.is_initialized:
            raise RuntimeError("可视化器未初始化")
        
        joint_positions = np.array(joint_positions)
        
        # 更新关节位置
        if self.use_free_flyer:
            if base_pose is not None:
                base_pose = np.array(base_pose)
                if len(base_pose) == 7:  # [x, y, z, qx, qy, qz, qw]
                    self.current_q[:7] = base_pose
                elif len(base_pose) == 6:  # [x, y, z, rx, ry, rz]
                    self.current_q[:3] = base_pose[:3]
                    # 将欧拉角转换为四元数
                    quat = pinocchio.utils.rpyToMatrix(base_pose[3:6])
                    self.current_q[3:7] = pinocchio.Quaternion(quat).coeffs()
            
            # 设置关节角度
            start_idx = 7
            end_idx = min(7 + len(joint_positions), self.model.nq)
            self.current_q[start_idx:end_idx] = joint_positions[:end_idx-start_idx]
        else:
            # 无浮动基座情况
            end_idx = min(len(joint_positions), self.model.nq)
            self.current_q[:end_idx] = joint_positions[:end_idx]
        
        # 更新显示
        self.display.display([self.current_q])
    
    def get_joint_names(self) -> List[str]:
        """获取关节名称列表"""
        if not self.is_initialized:
            return []
        
        joint_names = []
        for i in range(self.model.njoints):
            joint_names.append(self.model.names[i])
        return joint_names
    
    def get_frame_names(self) -> List[str]:
        """获取所有frame名称列表"""
        if not self.is_initialized:
            return []
        
        frame_names = []
        for i in range(self.model.nframes):
            frame_names.append(self.model.frames[i].name)
        return frame_names
    
    def get_frame_pose(self, frame_name: str) -> Optional[np.ndarray]:
        """
        获取指定frame的位姿
        
        Args:
            frame_name: frame名称
            
        Returns:
            4x4变换矩阵，如果frame不存在则返回None
        """
        if not self.is_initialized:
            return None
        
        try:
            frame_id = self.model.getFrameId(frame_name)
            pinocchio.forwardKinematics(self.model, self.data, self.current_q)
            pinocchio.updateFramePlacements(self.model, self.data)
            return self.data.oMf[frame_id].homogeneous
        except:
            return None
    
    def get_center_of_mass(self) -> np.ndarray:
        """获取质心位置"""
        if not self.is_initialized:
            return np.zeros(3)
        
        return pinocchio.centerOfMass(self.model, self.data, self.current_q)
    
    def start_animation_loop(self, animation_function, fps: float = 30.0):
        """
        启动动画循环
        
        Args:
            animation_function: 动画函数，应该返回关节位置数组
            fps: 动画帧率
        """
        if self.visualization_thread is not None:
            self.stop_animation_loop()
        
        self.stop_visualization = False
        
        def animation_worker():
            dt = 1.0 / fps
            while not self.stop_visualization:
                try:
                    joint_positions = animation_function()
                    if joint_positions is not None:
                        self.update_joint_positions(joint_positions)
                    time.sleep(dt)
                except Exception as e:
                    print(f"动画循环错误: {str(e)}")
                    break
        
        self.visualization_thread = threading.Thread(target=animation_worker)
        self.visualization_thread.daemon = True
        self.visualization_thread.start()
        
        print(f"动画循环已启动，帧率: {fps} FPS")
    
    def stop_animation_loop(self):
        """停止动画循环"""
        if self.visualization_thread is not None:
            self.stop_visualization = True
            self.visualization_thread.join(timeout=1.0)
            self.visualization_thread = None
            print("动画循环已停止")
    
    def reset_to_zero_position(self):
        """重置到零位"""
        if self.is_initialized:
            self.current_q = pinocchio.utils.zero(self.model.nq)
            if self.use_free_flyer:
                self.current_q[6] = 1.0  # 设置四元数w分量为1
            self.display.display([self.current_q])
    
    def __del__(self):
        """析构函数"""
        self.stop_animation_loop()


# 示例使用函数
def create_walking_animation(visualizer: RobotVisualizer) -> callable:
    """创建一个简单的步行动画函数"""
    phase = 0
    
    def walking_animation():
        nonlocal phase
        phase += 0.005
        
        sin_pos = np.sin(2 * np.pi * phase)
        sin_pos_l = sin_pos.copy()
        sin_pos_r = sin_pos.copy()
        
        # 创建12个关节的位置数组（假设是腿部关节）
        ref_dof_pos = np.zeros(12)
        scale_1 = 0.17
        scale_2 = 2 * scale_1
        
        # 左脚支撑相位设置为默认关节位置
        if sin_pos_l > 0:
            sin_pos_l = sin_pos_l * 0
        ref_dof_pos[0] = sin_pos_l * scale_1
        ref_dof_pos[3] = -sin_pos_l * scale_2
        ref_dof_pos[5] = sin_pos_l * scale_1
        
        # 右脚支撑相位设置为默认关节位置
        if sin_pos_r < 0:
            sin_pos_r = sin_pos_r * 0
        ref_dof_pos[6] = -sin_pos_r * scale_1
        ref_dof_pos[9] = sin_pos_r * scale_2
        ref_dof_pos[11] = -sin_pos_r * scale_1
        
        # 双支撑相位
        if np.abs(sin_pos) < 0.1:
            ref_dof_pos[:] = 0
        
        return ref_dof_pos
    
    return walking_animation
