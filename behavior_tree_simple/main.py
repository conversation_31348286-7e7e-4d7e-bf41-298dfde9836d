# main.py
import py_trees
from nodes.move_base import MoveTo
from nodes.detect_object import DetectObject
from nodes.pick import PickObject
from nodes.place import PlaceObject

def create_behavior_tree():
    root = py_trees.composites.Sequence("PickAndPlace", memory=False)

    root.add_children([
        MoveTo("导航到工位", target_pose="workstation_1"),
        DetectObject("检测物体"),
        PickObject("抓取物体"),
        MoveTo("导航到放置点", target_pose="tray_A"),
        PlaceObject("放置物体")
    ])

    return root

if __name__ == "__main__":
    tree_root = create_behavior_tree()
    behaviour_tree = py_trees.trees.BehaviourTree(root=tree_root)

    print("行为树开始执行...")

    # 初始化行为树
    behaviour_tree.setup(timeout=15)

    try:
        while True:
            behaviour_tree.tick()
            print(py_trees.display.unicode_tree(behaviour_tree.root, show_status=True))
            input(">>> 回车继续下一次 tick ...")
    except KeyboardInterrupt:
        print("\n中止行为树执行。")
