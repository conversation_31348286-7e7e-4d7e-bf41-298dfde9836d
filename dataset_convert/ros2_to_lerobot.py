import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy
from std_msgs.msg import Float32MultiArray
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
import numpy as np
import torch
from lerobot.common.datasets.lerobot_dataset import LeRobotDataset
from pathlib import Path
import time
from message_filters import ApproximateTimeSynchronizer, Subscriber

class LeRobotRecorder(Node):
    def __init__(self):
        super().__init__('lerobot_recorder')

        # 参数
        self.fps = 30  # 数据采集频率
        self.repo_id = "my_dataset/test_dataset"  # 数据集名称
        self.root_dir = Path.home() / ".cache" / "huggingface" / "lerobot" / "test_dataset"
        self.tolerance_s = 1e-4  # 时间同步容差
        self.episode_index = 0  # 当前 episode 索引

        # 初始化 LeRobotDataset
        self.features = {
            "action": {"dtype": "float32", "shape": [6]},  # 假设动作是 6 维向量
            "observation.state": {"dtype": "float32", "shape": [12]},  # 假设状态是 12 维向量
            "observation.head_image": {"dtype": "image", "shape": [480, 640, 3]},  # 图像分辨率 640x480
            "frame_index": {"dtype": "int32", "shape": []},
            "timestamp": {"dtype": "float32", "shape": []},
            "episode_index": {"dtype": "int32", "shape": []},
            "task_index": {"dtype": "int32", "shape": []},
        }
        self.dataset = LeRobotDataset.create(
            repo_id=self.repo_id,
            fps=self.fps,
            root=self.root_dir,
            features=self.features,
            use_videos=True,
            tolerance_s=self.tolerance_s,
            image_writer_processes=4,  # 并行保存图像
        )

        # CvBridge 用于 ROS 图像转换
        self.bridge = CvBridge()

        # QoS 设置
        qos = QoSProfile(
            reliability=ReliabilityPolicy.BEST_EFFORT,
            history=HistoryPolicy.KEEP_LAST,
            depth=10
        )

        # 订阅话题
        self.action_sub = Subscriber(self, Float32MultiArray, '/action', qos_profile=qos)
        self.state_sub = Subscriber(self, Float32MultiArray, '/state', qos_profile=qos)
        self.image_sub = Subscriber(self, Image, '/head_image', qos_profile=qos)

        # 时间同步器
        self.ts = ApproximateTimeSynchronizer(
            [self.action_sub, self.state_sub, self.image_sub],
            queue_size=10,
            slop=0.1  # 100ms 同步容差
        )
        self.ts.registerCallback(self.callback)

        # 记录开始时间
        self.start_time = time.time()
        self.frame_count = 0
        self.task = "example_task"  # 任务描述

        self.get_logger().info("LeRobot Recorder initialized. Waiting for synchronized messages...")

    def callback(self, action_msg, state_msg, image_msg):
        # 获取当前时间戳
        current_time = time.time() - self.start_time

        # 转换动作和状态
        action = np.array(action_msg.data, dtype=np.float32)
        state = np.array(state_msg.data, dtype=np.float32)

        # 转换图像
        cv_image = self.bridge.imgmsg_to_cv2(image_msg, desired_encoding="rgb8")
        image = torch.from_numpy(cv_image).to(torch.uint8)  # 转换为 torch 张量

        # 验证数据维度
        if action.shape != (6,) or state.shape != (12,) or image.shape != (480, 640, 3):
            self.get_logger().warn("Invalid data shape, skipping frame")
            return

        # 构造帧数据
        frame = {
            "action": action,
            "observation.state": state,
            "observation.head_image": image,
            "timestamp": current_time,
            "task": self.task,
        }

        # 添加帧到缓冲区
        self.dataset.add_frame(frame)
        self.frame_count += 1

        self.get_logger().info(f"Added frame {self.frame_count} at timestamp {current_time:.3f}s")

        # 示例：每 100 帧保存一个 episode
        if self.frame_count >= 100:
            self.get_logger().info(f"Saving episode {self.episode_index}...")
            self.dataset.save_episode()
            self.frame_count = 0
            self.episode_index += 1
            self.get_logger().info(f"Episode {self.episode_index} saved.")

    def shutdown(self):
        # 保存未完成的 episode
        if self.frame_count > 0:
            self.get_logger().info(f"Saving final episode {self.episode_index}...")
            self.dataset.save_episode()
        # 停止图像写入器
        self.dataset.stop_image_writer()
        self.get_logger().info("Recorder shutdown.")

def main(args=None):
    rclpy.init(args=args)
    recorder = LeRobotRecorder()
    try:
        rclpy.spin(recorder)
    except KeyboardInterrupt:
        recorder.get_logger().info("Received shutdown signal.")
        recorder.shutdown()
    finally:
        recorder.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
