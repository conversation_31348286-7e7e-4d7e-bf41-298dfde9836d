import rclpy
from rclpy.node import Node
from rclpy.executors import SingleThreadedExecutor
import py_trees
import py_trees_ros
import time

class WaitBehaviour(py_trees.behaviour.Behaviour):
    def __init__(self, name, duration):
        super(Wait<PERSON><PERSON><PERSON><PERSON>, self).__init__(name)
        self.duration = duration
        self.start_time = None

    def initialise(self):
        self.start_time = time.time()
        self.logger.info(f"{self.name}: Waiting for {self.duration} seconds")

    def update(self):
        if time.time() - self.start_time >= self.duration:
            return py_trees.common.Status.SUCCESS
        return py_trees.common.Status.RUNNING

class SimpleBehaviorTree(Node):
    def __init__(self):
        super().__init__("simple_behavior_tree")
        # 创建根节点
        root = py_trees.composites.Sequence(name="Root",memory=True)
        # 添加等待行为
        wait_behaviour = WaitBehaviour(name="Wait3Sec", duration=3)
        success_behaviour = py_trees.behaviours.Success(name="Success")
        # 组装行为树
        root.add_children([wait_behaviour, success_behaviour])
        self.behaviour_tree = py_trees_ros.trees.BehaviourTree(root=root)
        self.behaviour_tree.setup(timeout=5.0)
    def run(self):
        self.behaviour_tree.tick_tock(period_ms=1000.0)
        print("Behavior Tree Running")
        rclpy.spin(self.behaviour_tree.node)

class SendVisualMessage(py_trees.behaviour.Behaviour):
    def __init__(self, name: str,list,blackboard_variable: str ,node: Node):
        """
        构造函数中传入已经创建好的 ROS 2 节点
        """
        super(SendVisualMessage, self).__init__(name)
        self.node = node
        # 使用传入的节点创建发布器
        self.publisher = self.node.create_publisher(Int32MultiArray, '/perception/switch', 10)
        self.list = list
        self.blackboard_variable = blackboard_variable
        self.has_sent = False  # 用于防止重复发送（根据需求可调整）
        self.bb = py_trees.blackboard.Blackboard()
        # 标志位：如果在initialise中检测到target_pose存在，就跳过update逻辑
        self.skip_update = False
    def initialise(self):
        # 在初始化时尝试获取黑板变量
        try:
            target_pose = self.bb.get(self.blackboard_variable)
            if target_pose is not None:
                self.skip_update = True
                self.feedback_message = "target_pose already exists; skipping update."
            else:
                self.skip_update = False
                self.feedback_message = "target_pose not found; proceeding with update."
        except:
            pass
    def update(self):
        """
        每次 tick 时发送消息，这里只发送一次，发送成功后返回 SUCCESS
        """
        if self.skip_update:
            # 从黑板获取变量
            value = self.bb.get(self.blackboard_variable)

def NavAction_FromBlackboard(name:str,action_name: str, key:str):
    wait_for_server_timeout_sec = -3.0
    behaviour = py_trees_ros.action_clients.FromBlackboard(
        name=name,
        action_type=NavigateToPose,
        action_name=action_name,
        key=key,
        generate_feedback_message=lambda msg: str(msg),
        wait_for_server_timeout_sec=wait_for_server_timeout_sec
    )
    return behaviour

def NavAction_FromConstant(name:str,action_name: str,goal_pos=None):
    action_behaviour = py_trees_ros.action_clients.FromConstant(
        name=name,
        action_type=NavigateToPose,
        action_name=action_name,
        action_goal=goal_pos,  # noqa
        generate_feedback_message=lambda msg: str(msg)# str(list(msg.feedback.partial_sequence))
    )
    return action_behaviour

def RobotControl_FromConstant(name: str,service_name: str,messages: str,key_response: str = None):
    # service_client 示例
    service_behaviour = py_trees_ros.service_clients.FromConstant(
        name=name,
        service_type=String,
        service_name=service_name,# 服务名称
        service_request=String.Request(message=messages),
        key_response=key_response,  # 是否保存到黑板
        # wait_for_server_timeout_sec=-3.0
    )
    return service_behaviour