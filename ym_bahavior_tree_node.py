import py_trees
import py_trees_ros
import rclpy
from rclpy.node import Node
import socket
import json
import time
from std_msgs.msg import String
from threading import Lock

# Provided ChassisNavigationClient (unchanged, included for completeness)
class ChassisNavigationClient:
    def __init__(self, server_ip, server_port):
        self.server_ip = server_ip
        self.server_port = server_port
        self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.client_socket.settimeout(10)

    def connect(self):
        try:
            self.client_socket.connect((self.server_ip, self.server_port))
            print(f"Connected to server at {self.server_ip}:{self.server_port}")
        except Exception as e:
            print(f"Failed to connect to server: {e}")
            self.client_socket.close()

    def send_command(self, command):
        try:
            self.client_socket.sendall(command.encode('utf-8'))
            response = self.client_socket.recv(4096).decode('utf-8')
            return json.loads(response)
        except Exception as e:
            print(f"Error sending command: {e}")
            return None

    def move_to_marker(self, marker_name):
        command = f"/api/move?marker={marker_name}"
        print(f"Sending command: {command}")
        return self.send_command(command)

    def move_to_markers(self, markers, distance_tolerance=0.5, count=1, max_continuous_retries=5):
        if not markers or len(markers) < 2:
            print("Error: At least two markers are required for multi-target movement.")
            return None
        markers_param = ",".join(markers)
        command = f"/api/move?markers={markers_param}&distance_tolerance={distance_tolerance}&count={count}&max_continuous_retries={max_continuous_retries}"
        print(f"Sending command: {command}")
        return self.send_command(command)

    def get_robot_status(self):
        command = "/api/robot_status"
        return self.send_command(command)

    def get_power_status(self):
        command = "/api/get_power_status"
        print(f"Sending command: {command}")
        return self.send_command(command)

    def wait_for_move_to_complete(self, timeout=None, check_interval=1):
        print("Waiting for robot movement to complete...")
        start_time = time.time()
        while True:
            if timeout is not None and (time.time() - start_time) > timeout:
                print(f"Timeout of {timeout} seconds reached, cancelling move...")
                self.cancel_move()
                return False
            status = self.get_robot_status()
            if status and status.get("status") == "OK":
                move_status = status["results"].get("move_status")
                if move_status == "succeeded":
                    print("Move completed successfully.")
                    return True
                elif move_status == "failed":
                    print("Move failed.")
                    return False
                elif move_status == "running":
                    print("Move is still running, waiting...")
                else:
                    print(f"Unknown move status: {move_status}")
            else:
                print("Error fetching robot status, retrying...")
            time.sleep(check_interval)

    def cancel_move(self):
        command = "/api/move/cancel"
        print(f"Sending cancel command: {command}")
        return self.send_command(command)

    def close(self):
        self.client_socket.close()
        print("Connection closed.")

# Placeholder for visual inspection (replace with your actual vision module)
def start_visual_inspection():
    print("Starting visual inspection...")
    # Implement actual vision start logic
    return True

def stop_visual_inspection():
    print("Stopping visual inspection...")
    # Implement actual vision stop logic
    return True

# Placeholder for arm operations (replace with your actual arm control)
def perform_arm_task(task_name):
    print(f"Performing arm task: {task_name}")
    # Simulate arm operation (e.g., via ROS2 service or action)
    time.sleep(2)  # Simulate task duration
    return True

# Behavior: Update battery level via socket
class UpdateBatteryLevel(py_trees.behaviour.Behaviour):
    def __init__(self, name, nav_client):
        super().__init__(name)
        self.nav_client = nav_client
        self.blackboard = self.attach_blackboard_client(name)
        self.blackboard.register_key("battery_level", access=py_trees.common.Access.WRITE)
        self.blackboard.battery_level = 100.0
        self.last_update = 0.0
        self.update_interval = 300.0  # Update every 10 seconds

    def update(self):
        if time.time() - self.last_update >= self.update_interval:
            response = self.nav_client.get_power_status()
            if response and response.get("status") == "OK":
                self.blackboard.battery_level = response["results"].get("battery_capacity")
                self.logger.info(f"Updated battery level: {self.blackboard.battery_level}%")
                self.last_update = time.time()
                return py_trees.common.Status.SUCCESS
            else:
                self.logger.error("Failed to update battery level")
                return py_trees.common.Status.FAILURE
        return py_trees.common.Status.SUCCESS

# Behavior: Check if battery is low (< 10%)
class CheckLowBattery(py_trees.behaviour.Behaviour):
    def __init__(self, name):
        super().__init__(name)
        self.blackboard = self.attach_blackboard_client(name)
        self.blackboard.register_key("battery_level", access=py_trees.common.Access.READ)

    def update(self):
        if self.blackboard.battery_level < 10.0:
            self.logger.info("Battery low (< 10%)")
            return py_trees.common.Status.SUCCESS
        return py_trees.common.Status.FAILURE

# Behavior: Navigate to charging station and wait until charged
class Recharge(py_trees.behaviour.Behaviour):
    def __init__(self, name, nav_client):
        super().__init__(name)
        self.nav_client = nav_client
        self.blackboard = self.attach_blackboard_client(name)
        self.blackboard.register_key("battery_level", access=py_trees.common.Access.READ)
        self.state = "NAVIGATE"

    def setup(self, **kwargs):
        self.logger.info("Setting up recharge behavior")

    def update(self):
        if self.state == "NAVIGATE":
            response = self.nav_client.move_to_marker("charge_station")
            if response and response.get("status") == "OK":
                if self.nav_client.wait_for_move_to_complete(timeout=60):
                    self.state = "CHARGING"
                    self.logger.info("Reached charging station, starting charge")
                    return py_trees.common.Status.RUNNING
                else:
                    self.logger.error("Failed to reach charging station")
                    return py_trees.common.Status.FAILURE
            else:
                self.logger.error("Failed to send move to charge station command")
                return py_trees.common.Status.FAILURE
        elif self.state == "CHARGING":
            if self.blackboard.battery_level >= 80.0:  # Assume charged when > 80%
                self.state = "NAVIGATE"
                self.logger.info("Charging complete")
                return py_trees.common.Status.SUCCESS
            self.logger.info("Still charging...")
            return py_trees.common.Status.RUNNING

# Behavior: Check for operation command
class CheckOperationCommand(py_trees.behaviour.Behaviour):
    def __init__(self, name):
        super().__init__(name)
        self.blackboard = self.attach_blackboard_client(name)
        self.blackboard.register_key("operation_command", access=py_trees.common.Access.READ)

    def update(self):
        if self.blackboard.operation_command:
            self.logger.info(f"Operation command received: {self.blackboard.operation_command}")
            return py_trees.common.Status.SUCCESS
        return py_trees.common.Status.FAILURE

# Behavior: Execute operation task (cancel cruise, navigate, perform arm task)
class ExecuteOperationTask(py_trees.behaviour.Behaviour):
    def __init__(self, name, nav_client):
        super().__init__(name)
        self.nav_client = nav_client
        self.blackboard = self.attach_blackboard_client(name)
        self.blackboard.register_key("operation_command", access=py_trees.common.Access.READ)
        self.blackboard.register_key("operation_command", access=py_trees.common.Access.WRITE)
        self.state = "CANCEL"
        self.task_markers = {
            "task_1": "task_1_point",
            "task_2": "task_2_point",
            "task_3": "task_3_point"
        }

    def update(self):
        if not self.blackboard.operation_command:
            return py_trees.common.Status.FAILURE

        if self.state == "CANCEL":
            response = self.nav_client.cancel_move()
            if response and response.get("status") == "OK":
                self.state = "NAVIGATE"
                self.logger.info("Cancelled current navigation")
                return py_trees.common.Status.RUNNING
            else:
                self.logger.error("Failed to cancel navigation")
                return py_trees.common.Status.FAILURE
        elif self.state == "NAVIGATE":
            marker = self.task_markers.get(self.blackboard.operation_command)
            if not marker:
                self.logger.error(f"Unknown operation command: {self.blackboard.operation_command}")
                self.blackboard.operation_command = None
                return py_trees.common.Status.FAILURE
            response = self.nav_client.move_to_marker(marker)
            if response and response.get("status") == "OK":
                if self.nav_client.wait_for_move_to_complete(timeout=60):
                    self.state = "EXECUTE"
                    self.logger.info(f"Reached {marker} for {self.blackboard.operation_command}")
                    return py_trees.common.Status.RUNNING
                else:
                    self.logger.error(f"Failed to navigate to {marker}")
                    return py_trees.common.Status.FAILURE
            else:
                self.logger.error(f"Failed to send move command to {marker}")
                return py_trees.common.Status.FAILURE
        elif self.state == "EXECUTE":
            if perform_arm_task(self.blackboard.operation_command):
                self.blackboard.operation_command = None
                self.state = "CANCEL"
                self.logger.info(f"Completed {self.blackboard.operation_command}")
                return py_trees.common.Status.SUCCESS
            else:
                self.logger.error(f"Failed to execute arm task: {self.blackboard.operation_command}")
                self.blackboard.operation_command = None
                self.state = "CANCEL"
                return py_trees.common.Status.FAILURE

# Behavior: Perform multi-point cruising with visual inspection
class CruiseWithInspection(py_trees.behaviour.Behaviour):
    def __init__(self, name, nav_client):
        super().__init__(name)
        self.nav_client = nav_client
        self.markers = ["w_1", "w_2", "w_3"]  # Define your cruising markers
        self.inspection_active = False
        self.state = "START"

    def update(self):
        if self.state == "START":
            if start_visual_inspection():
                self.inspection_active = True
                response = self.nav_client.move_to_markers(self.markers, count=-1)  # Infinite loop
                if response and response.get("status") == "OK":
                    self.state = "CRUISING"
                    self.logger.info("Started multi-point cruising with inspection")
                    return py_trees.common.Status.RUNNING
                else:
                    self.logger.error("Failed to start multi-point cruising")
                    return py_trees.common.Status.FAILURE
            else:
                self.logger.error("Failed to start visual inspection")
                return py_trees.common.Status.FAILURE
        elif self.state == "CRUISING":
            status = self.nav_client.get_robot_status()
            if status and status.get("status") == "OK" and status["results"].get("move_status") == "running":
                return py_trees.common.Status.RUNNING
            elif status and status["results"].get("move_status") == "failed":
                self.logger.error("Cruising failed")
                self.state = "STOP"
                return py_trees.common.Status.FAILURE
            else:
                self.logger.info("Cruising interrupted or completed")
                self.state = "STOP"
                return py_trees.common.Status.SUCCESS
        elif self.state == "STOP":
            if self.inspection_active:
                stop_visual_inspection()
                self.inspection_active = False
            self.state = "START"
            return py_trees.common.Status.SUCCESS

# ROS2 Node to manage behavior tree and operation command subscription
class BehaviorTreeNode(Node):
    def __init__(self):
        super().__init__('behavior_tree_node')
        self.nav_client = ChassisNavigationClient("*************", 31001)
        self.nav_client.connect()
        self.blackboard = py_trees.blackboard.Blackboard()
        self.blackboard.set("battery_level", 100.0)
        self.blackboard.set("operation_command", None)
        self.command_sub = self.create_subscription(
            String, '/operation_command', self.command_callback, 10)
        self.command_lock = Lock()

        # Build behavior tree
        root = py_trees.composites.Parallel(
            name="Root",
            policy=py_trees.common.ParallelPolicy.SuccessOnAll(synchronise=False)
        )

        # Background tasks
        background = py_trees.composites.Sequence(name="Background", memory=True)
        background.add_children([
            UpdateBatteryLevel("UpdateBattery", self.nav_client),
            py_trees_ros.subscribers.ToBlackboard(
                name="OperationCommandToBB",
                topic_name="/operation_command",
                topic_type=String,
                qos_profile=1,
                blackboard_variables={"operation_command": "data"}
            )
        ])

        # Main tasks (priority selector)
        tasks = py_trees.composites.Selector(name="Tasks", memory=False)
        recharge_sequence = py_trees.composites.Sequence(name="RechargeSequence", memory=True)
        recharge_sequence.add_children([
            CheckLowBattery("CheckLowBattery"),
            Recharge("Recharge", self.nav_client)
        ])
        operation_sequence = py_trees.composites.Sequence(name="OperationSequence", memory=True)
        operation_sequence.add_children([
            CheckOperationCommand("CheckOperation"),
            ExecuteOperationTask("ExecuteOperation", self.nav_client)
        ])
        tasks.add_children([
            recharge_sequence,
            operation_sequence,
            CruiseWithInspection("CruiseWithInspection", self.nav_client)
        ])

        root.add_children([background, tasks])
        self.tree = py_trees_ros.trees.BehaviourTree(root, unicode_tree_debug=True)
        self.tree.setup(timeout=15.0, node=self)

    def command_callback(self, msg):
        with self.command_lock:
            self.blackboard.set("operation_command", msg.data)
            self.get_logger().info(f"Received operation command: {msg.data}")

    def tick(self):
        # self.tree.tick_once()
        self.tree.tick()

    def shutdown(self):
        self.nav_client.close()
        self.tree.shutdown()

def main():
    rclpy.init()
    node = BehaviorTreeNode()
    try:
        executor = rclpy.executors.SingleThreadedExecutor()
        executor.add_node(node)
        while rclpy.ok():
            node.tick()
            executor.spin_once(timeout_sec=0.1)
    except KeyboardInterrupt:
        node.get_logger().info("Shutting down behavior tree")
    finally:
        node.shutdown()
        rclpy.shutdown()

if __name__ == '__main__':
    main()


'''How to Run
Setup ROS2 Environment:
bash

source /opt/ros/jazzy/setup.bash  # Adjust for your ROS2 distro

Install Dependencies:
bash

sudo apt install ros-jazzy-py-trees ros-jazzy-py-trees-ros-interfaces ros-jazzy-py-trees-ros ros-jazzy-py-trees-ros-tutorials ros-jazzy-py-trees-ros-viewer
pip install py-trees

Save Code:
Save the code as behavior_tree_node.py in a ROS2 package (e.g., my_robot_pkg).

Run the Node:
bash

ros2 run my_robot_pkg behavior_tree_node

Send Operation Commands:
Use ros2 topic pub to send commands:
bash

ros2 topic pub /operation_command std_msgs/String "{data: 'task_1'}"

To clear the command:
bash

ros2 topic pub /operation_command std_msgs/String "{data: ''}"

Visualize Behavior Tree:
bash

py-trees-tree-watcher -b /tree/snapshots
ros2 run py_trees_ros_viewer py_trees_ros_viewer

Code Explanation
Behavior Tree Structure:
Root (Parallel): Runs background tasks and main tasks concurrently.

Background (Sequence):
UpdateBatteryLevel: Queries /api/get_power_status every 10 seconds, updates battery_level on blackboard.

ToBlackboard: Subscribes to /operation_command, updates operation_command on blackboard.

Tasks (Selector):
RechargeSequence: Triggers if battery < 10%, navigates to charge_station, waits until battery > 80%.

OperationSequence: Triggers if operation_command exists, cancels cruising, navigates to task-specific marker (e.g., task_1_point), performs arm...

Something went wrong, please refresh to reconnect or try again.

'''