#!/usr/bin/env python3
"""
机器人可视化模块使用示例
演示如何使用RobotVisualizer类进行实时可视化
"""

import numpy as np
import time
from robot_visualizer import RobotVisualizer, create_walking_animation


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建可视化器
    urdf_path = '/home/<USER>/humanoid-gym/resources/robots/XBot/urdf/XBot-L.urdf'
    package_dirs = ['/home/<USER>/humanoid-gym/resources/robots/XBot/']
    
    try:
        visualizer = RobotVisualizer(
            urdf_path=urdf_path,
            package_dirs=package_dirs,
            use_free_flyer=True
        )
        
        print("机器人模型加载成功！")
        print(f"关节名称: {visualizer.get_joint_names()[:10]}...")  # 显示前10个关节
        
        # 显示初始位置
        print("显示初始位置...")
        time.sleep(2)
        
        # 更新关节位置
        print("更新关节位置...")
        joint_positions = np.array([0.3, 0.1, 0.3, -0.5, -0.2, 0.0,  # 左腿
                                   0.3, 0.1, 0.3, -0.5, -0.2, 0.0,   # 右腿
                                   0.0, 0.0, 0.0,                     # 腰部
                                   0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3,  # 左臂
                                   0.0, 0.0, 0.0,                     # 头部
                                   0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3]) # 右臂
        
        visualizer.update_joint_positions(joint_positions)
        time.sleep(3)
        
        # 重置到零位
        print("重置到零位...")
        visualizer.reset_to_zero_position()
        time.sleep(2)
        
        return visualizer
        
    except Exception as e:
        print(f"创建可视化器失败: {str(e)}")
        return None


def example_individual_joint_movement(visualizer):
    """单个关节运动示例"""
    if visualizer is None:
        return
    
    print("\n=== 单个关节运动示例 ===")
    
    # 获取关节数量（排除浮动基座的7个自由度）
    num_joints = visualizer.model.nq - 7
    
    print(f"逐个测试 {num_joints} 个关节...")
    
    for i in range(min(num_joints, 20)):  # 限制测试前20个关节
        joint_positions = np.zeros(num_joints)
        joint_positions[i] = 1.0  # 设置第i个关节为1弧度
        
        visualizer.update_joint_positions(joint_positions)
        print(f"关节 {i+1}/{num_joints} 运动中...")
        time.sleep(0.5)
    
    # 重置
    visualizer.reset_to_zero_position()


def example_walking_animation(visualizer):
    """步行动画示例"""
    if visualizer is None:
        return
    
    print("\n=== 步行动画示例 ===")
    
    # 创建步行动画函数
    walking_func = create_walking_animation(visualizer)
    
    print("开始步行动画，持续10秒...")
    
    # 手动运行动画循环
    start_time = time.time()
    while time.time() - start_time < 10.0:
        joint_positions = walking_func()
        visualizer.update_joint_positions(joint_positions)
        time.sleep(1/30.0)  # 30 FPS
    
    # 重置
    visualizer.reset_to_zero_position()


def example_frame_information(visualizer):
    """Frame信息示例"""
    if visualizer is None:
        return
    
    print("\n=== Frame信息示例 ===")
    
    # 获取所有frame名称
    frame_names = visualizer.get_frame_names()
    print(f"总共有 {len(frame_names)} 个frames")
    
    # 查找脚部frames
    foot_frames = [name for name in frame_names if 'foot' in name.lower()]
    print(f"脚部相关frames: {foot_frames}")
    
    # 获取脚部位置
    for frame_name in foot_frames[:2]:  # 只显示前两个
        pose = visualizer.get_frame_pose(frame_name)
        if pose is not None:
            position = pose[:3, 3]
            print(f"{frame_name} 位置: {position}")
    
    # 获取质心位置
    com = visualizer.get_center_of_mass()
    print(f"质心位置: {com}")


def example_real_time_control():
    """实时控制示例"""
    print("\n=== 实时控制示例 ===")
    
    urdf_path = '/home/<USER>/humanoid-gym/resources/robots/XBot/urdf/XBot-L.urdf'
    package_dirs = ['/home/<USER>/humanoid-gym/resources/robots/XBot/']
    
    try:
        visualizer = RobotVisualizer(
            urdf_path=urdf_path,
            package_dirs=package_dirs,
            use_free_flyer=True
        )
        
        print("实时控制模式启动...")
        print("这里可以接收外部命令来更新关节位置")
        
        # 模拟实时控制
        for i in range(100):
            # 模拟从外部接收的关节位置数据
            t = i * 0.1
            joint_positions = np.sin(t) * 0.5 * np.ones(32)  # 32个关节的正弦波运动
            
            # 更新可视化
            visualizer.update_joint_positions(joint_positions)
            
            # 模拟控制频率
            time.sleep(0.05)  # 20 Hz
            
            if i % 20 == 0:
                print(f"更新步骤: {i}/100")
        
        print("实时控制示例完成")
        return visualizer
        
    except Exception as e:
        print(f"实时控制示例失败: {str(e)}")
        return None


def main():
    """主函数"""
    print("机器人可视化模块示例程序")
    print("=" * 50)
    
    # 基本使用示例
    visualizer = example_basic_usage()
    
    if visualizer is not None:
        # 单个关节运动示例
        example_individual_joint_movement(visualizer)
        
        # 步行动画示例
        example_walking_animation(visualizer)
        
        # Frame信息示例
        example_frame_information(visualizer)
    
    # 实时控制示例
    example_real_time_control()
    
    print("\n所有示例完成！")
    print("可视化窗口将保持打开状态，按Ctrl+C退出")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n程序退出")


if __name__ == "__main__":
    main()
