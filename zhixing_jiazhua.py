import minimalmodbus
import serial
import threading
import time

# 寄存器地址定义 - 控制相关
POSITION_HIGH_8 = 0x0102  # 位置寄存器高八位
POSITION_LOW_8 = 0x0103  # 位置寄存器低八位
SPEED = 0x0104
FORCE = 0x0105
MOTION_TRIGGER = 0x0108

# 寄存器地址定义 - 状态反馈相关
FEEDBACK_POS_HIGH = 0x0609  # 实时位置高16位
FEEDBACK_POS_LOW = 0x060A   # 实时位置低16位
FEEDBACK_SPEED = 0x060B      # 实时速度
FEEDBACK_FORCE = 0x060C      # 实时力矩（电流）
ALARM_INFO = 0x0612          # 报警信息

PORT = '/dev/ttyUSB0'  # 修改为您的COM口号
BAUD = 115200

instrument = minimalmodbus.Instrument(PORT, 1)
instrument.serial.baudrate = BAUD
instrument.serial.timeout = 1

lock = threading.Lock()


# ------------------- 控制功能 -------------------
# 写入高八位位置
def write_position_high8(instrument, value):
    with lock:
        instrument.write_register(POSITION_HIGH_8, value, functioncode=6)

# 写入低八位位置
def write_position_low8(instrument, value):
    with lock:
        instrument.write_register(POSITION_LOW_8, value, functioncode=6)

# 写入位置（使用long类型直接写入）
def write_position(instrument, value):
    with lock:
        instrument.write_long(POSITION_HIGH_8, value)

# 写入速度
def write_speed(instrument, speed):
    with lock:
        instrument.write_register(SPEED, speed, functioncode=6)

# 写入力矩
def write_force(instrument, force):
    with lock:
        instrument.write_register(FORCE, force, functioncode=6)

# 触发运动
def trigger_motion(instrument):
    with lock:
        instrument.write_register(MOTION_TRIGGER, 1, functioncode=6)


# ------------------- 状态反馈功能 -------------------
# 读取实时位置（合并高低位）
def read_current_position(instrument):
    with lock:
        try:
            pos_high = instrument.read_register(FEEDBACK_POS_HIGH, functioncode=3)
            pos_low = instrument.read_register(FEEDBACK_POS_LOW, functioncode=3)
            # 合并16位寄存器为32位值
            current_pos = (pos_high << 16) + pos_low
            return current_pos
        except Exception as e:
            print(f"读取位置失败: {e}")
            return None

# 读取实时速度
def read_current_speed(instrument):
    with lock:
        try:
            current_speed = instrument.read_register(FEEDBACK_SPEED, functioncode=3)
            return current_speed
        except Exception as e:
            print(f"读取速度失败: {e}")
            return None

# 读取实时力矩（电流）
def read_current_force(instrument):
    with lock:
        try:
            current_force = instrument.read_register(FEEDBACK_FORCE, functioncode=3)
            return current_force
        except Exception as e:
            print(f"读取力矩失败: {e}")
            return None

# 读取报警信息
def read_alarm_info(instrument):
    with lock:
        try:
            alarm_code = instrument.read_register(ALARM_INFO, functioncode=3)
            return alarm_code
        except Exception as e:
            print(f"读取报警信息失败: {e}")
            return None

# 解析报警代码为可读信息
def parse_alarm(alarm_code):
    alarm_map = {
        0x01: "过温警报",
        0x02: "堵转警报",
        0x04: "超速警报",
        0x08: "初始化故障警报",
        0x10: "超限检测警报",
        0x20: "夹取掉落警报"
    }
    alarms = []
    for code, msg in alarm_map.items():
        if alarm_code & code:
            alarms.append(msg)
    return alarms if alarms else ["无报警"]


# ------------------- 主程序 -------------------
if __name__ == '__main__':
    try:
        # 发送控制指令
        print("发送控制指令...")
        write_position(instrument, 5000)    # 设置位置
        write_speed(instrument, 80)         # 设置速度
        write_force(instrument, 50)         # 设置力矩
        trigger_motion(instrument)          # 触发运动
        
        # 实时读取状态（循环5次，每秒一次）
        print("\n开始读取夹爪状态:")
        for i in range(5):
            pos = read_current_position(instrument)
            speed = read_current_speed(instrument)
            force = read_current_force(instrument)
            alarm = read_alarm_info(instrument)
            
            print(f"\n--- 状态读取 {i+1} ---")
            print(f"当前位置: {pos}")
            print(f"当前速度: {speed}")
            print(f"当前力矩: {force} mA")
            print(f"报警信息: {parse_alarm(alarm)}")
            
            time.sleep(1)  # 间隔1秒
            
    except serial.SerialException as se:
        print(f"串口连接错误: {se}")
    except Exception as e:
        print(f"运行错误: {e}")
    finally:
        print("\n程序结束")